import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Decimal } from "@/generated/prisma/runtime/library";
import { $Enums } from "@/generated/prisma";
import DiscountDeleteProduct from "./DiscountDeleteProduct";

interface iPropProducts  {
    id: string;
    product: {
        FinalPrice: Decimal | null;
        HasDiscount: boolean;
        activeDiscountType: $Enums.DiscountType | null;
        activeDiscountValue: Decimal | null;
        discountPercentage: Decimal | null;
        id: string;
        Material_Number: string;
        Material_Group: string | null;
        PretAM: Decimal | null;
        createdAt: Date;
        Net_Weight: string | null;
        Description_Local: string | null;
        last_updated_at: Date;
    };
    productId: string;
    discountId: string;
    createdAt: Date;
    updatedAt: Date;
}

interface iPropDiscount {
    id: string;
    type: $Enums.DiscountType;
    value: Decimal;
    name: string;
}

export default function DiscountTableWithProducts({products, discount}: {products: iPropProducts[], discount: iPropDiscount}){
    return(
    <Table className="mt-6">
        <TableHeader>
            <TableRow>
                <TableHead>Material_Number</TableHead>
                <TableHead>Material_Group</TableHead>
                <TableHead>PretAM</TableHead>
                <TableHead>Pret Final</TableHead>
                <TableHead>Action</TableHead>
            </TableRow>
        </TableHeader>
        <TableBody>
            {products.map((product) => (
                <TableRow key={product.id}>
                    <TableCell>{product.product.Material_Number}</TableCell>
                    <TableCell>{product.product.Material_Group}</TableCell>
                    <TableCell>{product.product.PretAM ? product.product.PretAM.toFixed(2) : "N/A"}</TableCell>
                    <TableCell>{product.product.FinalPrice ? product.product.FinalPrice.toFixed(2) : "N/A"}</TableCell>              
                    <TableCell>
                        <DiscountDeleteProduct productId={product.productId} discountId={discount.id} />
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    </Table>
    )
}