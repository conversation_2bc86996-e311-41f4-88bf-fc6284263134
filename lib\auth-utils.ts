"server-only";

import { auth } from "@clerk/nextjs/server";
import prisma from "@/app/utils/db";
import { redirect } from "next/navigation";
import { cache } from "react";

export const requireAdmin = cache(async () => {
  const { userId } = await auth();
  
  if (!userId) {
    redirect("/sign-in");
  }
  
  const user = await prisma.user.findFirst({
    where: { externalId: userId },
    select: { 
      role: true,
      firstName: true,
      lastName: true,
      email: true,
      id: true,
      isActive: true,
      isSuspended: true,
      createdAt: true,
      profileImage: true,
      emailVerified: true,
      twoFactorEnabled: true,
      newsletterOptIn: true,
      lastLoginAt: true,
    }
  });
  
  if (!user || (user.role !== 'administAB') || !user.isActive || user.isSuspended) {
    redirect("/unauthorized");
  }

  // Update last activity
  await prisma.user.update({
    where: { id: user.id },
    data: { lastActivityAt: new Date() }
  });
  
  return user;
})

export const requireAdminOrModerator = cache(async () => {
  const { userId } = await auth();
  
  if (!userId) {
    redirect("/sign-in");
  }
  
  const user = await prisma.user.findFirst({
    where: { externalId: userId },
    select: { 
      role: true,
      firstName: true,
      lastName: true,
      email: true,
      id: true,
      isActive: true,
      isSuspended: true,
      createdAt: true,
      profileImage: true,
      emailVerified: true,
      passwordEnabled: true,
      twoFactorEnabled: true,
      newsletterOptIn: true,
      lastLoginAt: true,
     }
  });
  
  if (!user || (user.role !== 'administAB' && user.role !== 'moderatorAB') || !user.isActive || user.isSuspended) {
    redirect("/unauthorized");
  }

  // Update last activity
  await prisma.user.update({
    where: { id: user.id },
    data: { lastActivityAt: new Date() }
  });
  
  return user;
})

