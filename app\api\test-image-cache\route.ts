import { getImageIndexStats, getImagesForMaterial } from "@/lib/imageCache";
import { NextRequest, NextResponse } from "next/server";

/**
 * Test endpoint to verify the image cache system is working
 * GET /api/test-image-cache
 * GET /api/test-image-cache?material=ABC123
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const materialNumber = searchParams.get("material");

    if (materialNumber) {
      // Test specific material
      const images = await getImagesForMaterial(materialNumber);
      
      return NextResponse.json({
        success: true,
        materialNumber,
        totalImages: images.length,
        images: images.map(img => ({
          fileName: img.fileName,
          sequenceNumber: img.sequenceNumber,
          fileExtension: img.fileExtension,
          fileSize: img.fileSize,
          url: `/api/images/${materialNumber}${img.sequenceNumber > 0 ? `?sequence=${img.sequenceNumber}` : ""}`
        }))
      });
    } else {
      // Test overall stats
      const stats = await getImageIndexStats();
      
      return NextResponse.json({
        success: true,
        message: "Image cache system is working",
        stats
      });
    }

  } catch (error) {
    console.error("Test image cache error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
