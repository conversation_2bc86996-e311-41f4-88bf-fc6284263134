"use client";

import { format } from "date-fns";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";

interface UserSecuritySettingsProps {
  userId: string;
  lastLoginAt?: string | null;

  // Account lifecycle
  isActive: boolean;
  inactiveBy?: string;
  inactiveAt?: string;
  inactiveReason?: string;

  isSuspended: boolean;
  suspendedBy?: string;
  suspendedAt?: string;
  suspensionReason?: string;

  deletedAt?: string;
  deletedBy?: string;
  deletedReason?: string;

  // Security
  passwordEnabled: boolean;
  twoFactorEnabled: boolean;
  mfaEnabledAt?: string;
  mfaDisabledAt?: string;

  // Login security
  lockoutUntil?: string;
}

export default function UserSecuritySettings({
  lastLoginAt,

  isActive,
  inactiveBy,
  inactiveAt,
  inactiveReason,

  isSuspended,
  suspendedBy,
  suspendedAt,
  suspensionReason,

  deletedAt,
  deletedBy,
  deletedReason,

  passwordEnabled,
  twoFactorEnabled,
  mfaEnabledAt,
  mfaDisabledAt,

  lockoutUntil,
}: UserSecuritySettingsProps) {
  const fmt = (d?: string) => (d ? format(new Date(d), "PPP p") : "—");

  return (
    <div className="space-y-6">
      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>When was the last successful login</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-700 dark:text-gray-300">
            Last login: {lastLoginAt ? fmt(lastLoginAt) : "Never"}
          </p>
        </CardContent>
      </Card>

      {/* Account Status */}
      <Card>
        <CardHeader>
          <CardTitle>Account Status</CardTitle>
          <CardDescription>Active, deactivated or deleted state</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Currently active</span>
            <Badge variant={isActive ? "outline" : "destructive"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
          </div>

          {!isActive && (
            <>
              <Separator />
              <p className="text-sm">
                <strong>Deactivated by:</strong> {inactiveBy ?? "—"}
              </p>
              <p className="text-sm">
                <strong>At:</strong> {fmt(inactiveAt)}
              </p>
              <p className="text-sm">
                <strong>Reason:</strong> {inactiveReason ?? "—"}
              </p>
            </>
          )}

          {deletedAt && (
            <>
              <Separator />
              <p className="text-sm">
                <strong>Deleted at:</strong> {fmt(deletedAt)}
              </p>
              <p className="text-sm">
                <strong>By:</strong> {deletedBy ?? "—"}
              </p>
              <p className="text-sm">
                <strong>Reason:</strong> {deletedReason ?? "—"}
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Suspension */}
      {isSuspended && (
        <Card>
          <CardHeader>
            <CardTitle>Suspension Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="text-sm">
              <strong>Suspended by:</strong> {suspendedBy ?? "—"}
            </p>
            <p className="text-sm">
              <strong>At:</strong> {fmt(suspendedAt)}
            </p>
            <p className="text-sm">
              <strong>Reason:</strong> {suspensionReason ?? "—"}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Authentication</CardTitle>
          <CardDescription>Manage password and MFA settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span  className="text-sm text-gray-700 dark:text-gray-300">Password Login</span>
            <Switch checked={passwordEnabled} />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Two-Factor Authentication</span>
            <Switch checked={twoFactorEnabled} />
          </div>

          <div className="text-sm">
            <p>
              <strong>First MFA enabled on:</strong> {fmt(mfaEnabledAt)}
            </p>
            <p>
              <strong>Last disabled on:</strong> {fmt(mfaDisabledAt)}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Login Security */}
      <Card>
        <CardHeader>
          <CardTitle>Login Security</CardTitle>
          <CardDescription>Brute-force protection</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Lockout until</span>
            <span>{lockoutUntil ? fmt(lockoutUntil) : "Not locked"}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}


// "use client";

// import { useState } from "react";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { Shield, Key, AlertTriangle } from "lucide-react";
// import { toast } from "sonner";
// import { useRouter } from "next/navigation";


// interface UserSecuritySettingsProps {
//   userId: string;
//   lastLoginAt?: string | null;
//   userPassword?: boolean;
//   userPhone: string | null;
//   userEmail?: string;
//   user2FA?: boolean
// }

// export default function UserSecuritySettings({ 
//   userId, 
//   lastLoginAt ,
//   userPassword,
//   userPhone,
//   userEmail,
//   user2FA
// }: UserSecuritySettingsProps) {
//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           <Shield className="h-5 w-5" />
//           Security Settings
//         </CardTitle>
//         <CardDescription>
//           Manage account security settings
//         </CardDescription>
//       </CardHeader>
//       <CardContent className="space-y-6">

        
    
//         <div>
//           <h3 className="text-sm font-medium mb-2">Recent Activity</h3>
//           <p className="text-sm text-gray-500">
//             Last login: {lastLoginAt ? lastLoginAt : "Never"}
//           </p>
//         </div>
//       </CardContent>
//     </Card>
//   );
// }

