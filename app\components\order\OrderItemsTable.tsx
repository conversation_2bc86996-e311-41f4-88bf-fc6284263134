import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatCurrency } from "@/app/utils/formatters";
import Link from "next/link";

interface OrderItemProps {
  id: string;
  name: string | null;
  code: string;
  quantity: number;
  price: number;
  notes: string | null;
  notesToInvoice: boolean;
}

export default function OrderItemsTable({ items }: { items: OrderItemProps[] | undefined }) {
  if (!items || items.length === 0) {
    return <p className="text-muted-foreground">No items in this order</p>;
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Product</TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead>Notes to Invoice</TableHead>
            <TableHead className="text-right">Price</TableHead>
            <TableHead className="text-right">Quantity</TableHead>
            <TableHead className="text-right">Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id}>
              <TableCell>
                <Link 
                  href={`/product/${item.code}`}
                  className="font-medium hover:underline"
                >
                  {item.name}
                </Link>
              </TableCell>
              <TableCell>{item.code || 'N/A'}</TableCell>
              <TableCell>{item.notes}</TableCell>
              <TableCell>{item.notesToInvoice ? "Yes" : "No"}</TableCell>
              <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
              <TableCell className="text-right">{item.quantity}</TableCell>
              <TableCell className="text-right">{formatCurrency(item.price * item.quantity)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}