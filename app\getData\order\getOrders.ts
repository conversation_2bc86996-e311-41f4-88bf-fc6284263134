
import prisma from "@/app/utils/db";
import { OrderStatus, Prisma } from "@/generated/prisma";
import { getCurrentUser } from "../user/data";
import { logError } from "@/lib/logger";
import { z } from "zod";
import { cache } from "react";
import { ReturnOrders } from "@/types/order";

interface GetOrdersParams {
  query?: string;
  status?: OrderStatus;
  page?: number;
  perPage?: number;
  sort?: string;
  order?: "asc" | "desc";
}

export const getOrders = cache(async ({
  query,
  status,
  page = 1,
  perPage = 10,
  sort = "createdAt",
  order = "desc"
}: GetOrdersParams = {}) => {

  const user = await getCurrentUser()
  const userEmail = user?.email

  try{
    //validate parameters with zod and create the schema
    const getOrdersSchema = z.object({
      query: z.string().optional(),
      status: z.enum(Object.values(OrderStatus) as [string, ...string[]]).optional(),
      page: z.number().min(1).optional(),
      perPage: z.number().min(1).max(100).optional(),
      sort: z.string().optional(),
      order: z.enum(["asc", "desc"]).optional(),
    });

    const validatedParams = getOrdersSchema.safeParse({
      query, status, page, perPage, sort, order
    });

    if (!validatedParams.success) {
      logError(`getOrders -> Error at parsing: ${validatedParams.error} by ${userEmail}`)
      return { orders: [], totalOrders: 0, totalPages: 0 };
    }

    // Build where clause
    const where: Prisma.OrderWhereInput = {
      isActive: true,
    };

    // Apply filters
    if (query) {
      where.OR = [
        { orderNumber: { contains: query, mode: 'insensitive' } },
        { user: {
          OR: [
            { email: { contains: query, mode: 'insensitive' } },
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
          ]
        }},
      ];
    }

    if (status) {
      where.orderStatus = status;
    }

    // Calculate pagination
    const skip = (page - 1) * perPage;

    // Validate sort field
    const validSortFields = ['orderNumber', 'createdAt', 'amount', 'totalAmount', 'orderStatus', 'paymentStatus'];
    const safeSort = validSortFields.includes(sort) ? sort : 'createdAt';

    // Execute query with pagination
    const [orders, totalOrders] = await Promise.all([
      prisma.order.findMany({
        where,
        select: {
          id: true,
          orderNumber: true,
          amount: true,
          totalAmount: true,
          isPaid: true,
          orderStatus: true,
          paymentStatus: true,
          createdAt: true,
          notes: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { [safeSort]: order },
        skip,
        take: perPage,
      }),
      prisma.order.count({ where })
    ]);

    const typedOrders = orders.map((o) => ({
      user: {
        email: o.user.email,
        firstName: o.user.firstName,
        lastName: o.user.lastName,
      },
      id: o.id,
      orderNumber: o.orderNumber,
      amount: o.amount.toNumber(), // Convert Decimal to number
      totalAmount: o.totalAmount.toNumber(), // Convert Decimal to number
      isPaid: o.isPaid,
      notes: o.notes,
      orderStatus: o.orderStatus,
      paymentStatus: o.paymentStatus,
      createdAt: o.createdAt.toISOString(), // Convert Date to ISO string
    })) as ReturnOrders[];

    const totalPages = Math.ceil(totalOrders / perPage);

    return { orders: typedOrders, totalOrders, totalPages };
  } catch(e) {
    logError(`getOrders -> Unexpected error: ${e} for ${userEmail}`)
    return { orders: [], totalOrders: 0, totalPages: 0 };
  }
});

export async function getOrderById(id: string) {
  const user = await getCurrentUser()
  const userEmail = user?.email
  try{
    //validate parameter with zod
    const validatedId = z.string().cuid().safeParse(id);
    if (!validatedId.success) {
      logError(`getOrderById -> Error at parsing: ${validatedId.error} by ${userEmail}`)
      return null;
    }
    id = validatedId.data


    const order = await prisma.order.findUnique({
      where: {
        id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        showroom: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });
   
    if (!order) {
      logError(`getOrderById -> Order not found with ID ${id} by ${userEmail}`)
      return null;
    }
    return order;
  }
  catch(e){
    logError(`getOrderById -> Unexpected error: ${e} for ${userEmail}`)
    return null
  }
}
