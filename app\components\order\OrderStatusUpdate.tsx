"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export default function OrderStatusUpdate({ 
  orderId,
  invoiceAMprop, 
  currentStatus 
}: { 
  orderId: string; 
  invoiceAMprop: string | null;
  currentStatus: string;
}) {
  const router = useRouter();
  const [status, setStatus] = useState(currentStatus);
  const [note, setNote] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [invoiceAM, setInvoiceAM] = useState(invoiceAMprop || "");

  const statusOptions = [
    { value: "plasata", label: "<PERSON><PERSON><PERSON>" },
    { value: "completa", label: "Inchisa" },
    { value: "anulata", label: "Anulata" }
  ];

  const handleSubmit = async () => {
    if (status === currentStatus && !note.trim() && invoiceAM === invoiceAMprop) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          note: note.trim() || undefined,
          invoiceAM: invoiceAM.trim() || undefined,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to update order status");
      }
      
      router.refresh();
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">
          Update Status
        </label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-between">
          {/* Input with label for Invoice */}
          <div className="flex-1">
            <Label className="block text-sm font-medium mb-1">
              Factura AutoMaster
            </Label>
            <Input
              type="text"
              value={invoiceAM || ""}
              onChange={(e) => setInvoiceAM(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
      </div>
      
      <div>
        <Label className="block text-sm font-medium mb-1">
          Note (reason)
        </Label>
        <Textarea
          placeholder="Add a note about this status change"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          rows={3}
        />
      </div>
      
      <Button 
        onClick={handleSubmit} 
        //disabled={status === currentStatus || isSubmitting || !invoiceAM}
        className="w-full"
      >
        {isSubmitting ? "Updating..." : "Update Status"}
      </Button>
    </div>
  );
}