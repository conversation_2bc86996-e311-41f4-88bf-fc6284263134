import { Metada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, Upload } from "lucide-react";
import { Suspense } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getUsers } from "@/app/getData/user/data";
import { getUserStats } from "@/app/getData/user/stats";
import SyncUsersButton from "@/app/components/user/SyncUsersButton";
import UserFilters from "@/app/components/user/UserFilters";
import UserList from "@/app/components/user/UserList";



export const metadata: Metadata = {
  title: "User Management",
  description: "Manage users and permissions",
};

interface UsersPageProps {
  searchParams: Promise<{
    query?: string;
    role?: string;
    status?: string;
    page?: string;
    perPage?: string;
    sort?: string;
    order?: string;
  }>;
}

export default async function UsersPage({ searchParams }: UsersPageProps) {
  const user = await requireAdminOrModerator();
  if (!user) {
    return null;
  }

  // Await the searchParams Promise
  const params = await searchParams;
  
  const page = Number(params.page) || 1;
  const perPage = Number(params.perPage) || 10;
  const sort = params.sort || "createdAt";
  //const order = params.order || "desc";
  
  const { users, totalUsers, totalPages } = await getUsers({
    query: params.query,
    role: params.role,
    status: params.status,
    page,
    perPage,
    sort,
    order: (params.order === "asc" || params.order === "desc") ? params.order : "desc",
  });
  
  const stats = await getUserStats();
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Management</h1>
          <p className="text-gray-500 mt-1">Manage and monitor user accounts</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <SyncUsersButton />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeUsers}</div>
            <CardDescription>
              {stats.activePercentage}% of total
            </CardDescription>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">New Users (30d)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.newUsers30Days}</div>
            <CardDescription>
              {stats.newUsersGrowth > 0 ? "+" : ""}{stats.newUsersGrowth}% from previous
            </CardDescription>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Logged In (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.loggedInLast24h}</div>
          </CardContent>
        </Card>
      </div>
      
      <Suspense fallback={<div>Loading filters...</div>}>
        <UserFilters />
      </Suspense>
      
      <UserList
        users={users} 
        totalUsers={totalUsers} 
        totalPages={totalPages}
        currentPage={page}
        perPage={perPage}
      />
    </div>
  );
}
