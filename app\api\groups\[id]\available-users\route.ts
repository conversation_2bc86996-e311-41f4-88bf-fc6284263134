import { NextRequest, NextResponse } from "next/server";
import { getUsersNotInGroup } from "@/app/getData/user/data";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  console.log(request.url);
  try {
    // Check authentication and authorization
    await requireAdminOrModerator();
    
    const paramsObject = await params;
    const groupId = paramsObject.id;

    if (!groupId) {
      return NextResponse.json(
        { error: "Group ID is required" },
        { status: 400 }
      );
    }

    const users = await getUsersNotInGroup(groupId);
    
    return NextResponse.json(users);
  } catch (error) {
    console.error("Error fetching available users:", error);
    return NextResponse.json(
      { error: "Failed to fetch available users" },
      { status: 500 }
    );
  }
}
