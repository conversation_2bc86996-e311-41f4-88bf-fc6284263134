import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { logError } from "@/lib/logger";
import { NETWORK_PATHS } from "@/config/constants";

// [...path]

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const paramsObject = await params;
    const imagePath = paramsObject.path.join('/');
    
    // Construct the full path to the image
    const fullPath = path.join(NETWORK_PATHS.PICTURES_STORAGE, imagePath);
    
    // Security check: ensure the path is within the pictures directory
    const normalizedPicturesPath = path.resolve(NETWORK_PATHS.PICTURES_STORAGE);
    const normalizedFullPath = path.resolve(fullPath);
    
    if (!normalizedFullPath.startsWith(normalizedPicturesPath)) {
      logError(`API serveImage -> Path traversal attempt: ${imagePath}`);
      return NextResponse.json(
        { error: "Invalid path" },
        { status: 400 }
      );
    }

    // Check if file exists
    try {
      await fs.promises.access(fullPath);
    } catch {
      logError(`API serveImage -> File not found: ${fullPath}`);
      return NextResponse.json(
        { error: "Image not found" },
        { status: 404 }
      );
    }

    // Read the file
    const fileBuffer = await fs.promises.readFile(fullPath);
    
    // Determine content type based on file extension
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
    }

    // Return the image with appropriate headers
    return new NextResponse(fileBuffer as BodyInit, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });

  } catch (error) {
    logError(`API serveImage -> Unexpected error:`, error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
