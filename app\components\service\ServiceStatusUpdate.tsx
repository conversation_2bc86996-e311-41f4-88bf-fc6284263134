"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ServiceStatus, ResolutionType } from "@/generated/prisma";
import { toast } from "sonner";

interface ServiceStatusUpdateProps {
  serviceRequest: {
    id: string;
    status: ServiceStatus;
    resolution?: ResolutionType | null;
  };
}

export default function ServiceStatusUpdate({ serviceRequest }: ServiceStatusUpdateProps) {
  const [newStatus, setNewStatus] = useState<ServiceStatus>(serviceRequest.status);
  const [resolution, setResolution] = useState<ResolutionType | "NONE">(serviceRequest.resolution || "NONE");
  const [notes, setNotes] = useState("");
  const [resolutionNotes, setResolutionNotes] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleStatusUpdate = async () => {
    if (newStatus === serviceRequest.status && !notes.trim()) {
      toast.error("Please select a different status or add notes");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/services/${serviceRequest.id}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          notes: notes.trim() || undefined,
          resolution: resolution === "NONE" ? undefined : resolution,
          resolutionNotes: resolutionNotes.trim() || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update service status");
      }

      toast.success("Service status updated successfully");
      router.refresh();
      setNotes("");
      setResolutionNotes("");
    } catch (error) {
      console.error("Error updating service status:", error);
      toast.error("Failed to update service status");
    } finally {
      setIsLoading(false);
    }
  };

  const statusOptions = Object.values(ServiceStatus).map((status) => ({
    value: status,
    label: status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
  }));

  const resolutionOptions = Object.values(ResolutionType).map((type) => ({
    value: type,
    label: type.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
  }));

  const showResolutionField = newStatus === ServiceStatus.completed || serviceRequest.status === ServiceStatus.completed;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Update Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            New Status
          </label>
          <Select value={newStatus} onValueChange={(value: ServiceStatus) => setNewStatus(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {showResolutionField && (
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Resolution Type
            </label>
            <Select value={resolution} onValueChange={(value: ResolutionType | "NONE") => setResolution(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select resolution type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="NONE">No resolution selected</SelectItem>
                {resolutionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Status Update Notes
          </label>
          <Textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this status change..."
            rows={3}
          />
        </div>

        {showResolutionField && (
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Resolution Notes
            </label>
            <Textarea
              value={resolutionNotes}
              onChange={(e) => setResolutionNotes(e.target.value)}
              placeholder="Add details about the resolution..."
              rows={3}
            />
          </div>
        )}

        <Button 
          onClick={handleStatusUpdate} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? "Updating..." : "Update Status"}
        </Button>
      </CardContent>
    </Card>
  );
}
