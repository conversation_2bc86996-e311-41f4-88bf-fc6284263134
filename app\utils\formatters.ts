export function formatCurrency(amount: number | string | null | undefined): string {
  if (amount === null || amount === undefined) return "N/A";
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  return new Intl.NumberFormat('ro-RO', {
    style: 'currency',
    currency: 'RON',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numAmount);
}

export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return "N/A";
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj);
}

export function formatOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    plasata: "Deschisa",
    completa: "<PERSON>hisa",
    procesata: "Processing",
    expediata: "Shipped",
    livrata: "Delivered",
    anulata: "Anulata"
  };
  
  return statusMap[status] || status;
}