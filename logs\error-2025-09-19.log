{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:15:40"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:16:05"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:18:22"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:18:41"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:27:23"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-19 09:44:29"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /classes couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /dashboard couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route / couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /product couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /profile couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /orders couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /returns couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /services couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /unauthorized couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /categories couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /settings couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /sign-out couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /users couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /_not-found couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /featured couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /analytics couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /_not-found couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /analytics couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /returns couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /profile couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route / couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /product couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /settings couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /unauthorized couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /services couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /dashboard couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /classes couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /categories couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /sign-out couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /users couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /orders couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /featured couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 13:45:26"}
