"use client";

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import <PERSON> from 'papaparse';
import { ProductAddAttributeCSVAction } from "@/app/actions/actions";
import { ProductAddAttributeFormValues } from "@/app/zod/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";


export default function ProductAddAttributeCSV() {
  const [isPending, startTransition] = useTransition();
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    setFile(selectedFile || null);
  };

  const handleFileUpload = () => {
    if (!file) {
      toast.error("Please select a CSV file.");
      return;
    }

    Papa.parse(file, {
      header: true,
      delimiter: ";",
      skipEmptyLines: true,
      complete: async (results) => {
        const rows = results.data as Record<string, string>[];

        const parsedData: ProductAddAttributeFormValues[] = rows
          .map((row) => {
            const materialNumber = row.materialNumber?.trim();
            const key = row.key?.trim();
            const value = row.value?.trim();

            if (!materialNumber || !key || !value) return null;

            // Ensure 11-character format for material numbers
            const normalizedMaterialNumber =
              materialNumber.length === 10 ? `0${materialNumber}` : materialNumber;

            return {
              materialNumber: normalizedMaterialNumber,
              key,
              value,
            };
          })
          .filter(Boolean) as ProductAddAttributeFormValues[];

        if (parsedData.length === 0) {
          toast.error("CSV appears to be empty or malformed.");
          return;
        }

        startTransition(async () => {
          try {
            const response = await ProductAddAttributeCSVAction(parsedData);

            if (response.status === "SUCCESS") {
              toast.success(response.message);
            } else {
              toast.error(response.message);
            }
          } catch (error) {
            toast.error("An unexpected error occurred.");
            console.error(error);
          }
        });
      },
      error: (err) => {
        toast.error("Failed to parse CSV file.");
        console.error(err);
      },
    });
  };

  return (
      <Card>
        <CardHeader><CardTitle>Add New Attributes with CSV file</CardTitle></CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Upload a CSV file with the following columns: materialNumber, key, value
          </p>
          <div className="flex gap-4 items-center mb-4">
            <input type="file" accept=".csv" onChange={handleFileChange} />
            <Button
              variant="default"
              onClick={handleFileUpload}
              disabled={isPending || !file}
            >
              {isPending ? "Processing..." : "Upload and Add Attributes"}
            </Button>
          </div>
        </CardContent>
      </Card>
  );
}
