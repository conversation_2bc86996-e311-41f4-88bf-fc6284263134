"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { 
  CreateShowroomFormValues,
  UpdateShowroomFormValues,
  DeleteShowroomFormValues,
  createShowroomSchema,
  updateShowroomSchema,
  deleteShowroomSchema
} from "@/app/zod/showroomSchemas";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export type ShowroomAction = {
  status: "SUCCESS" | "ERROR";
  message: string;
  data?: any;
  fieldErrors?: Record<string, string>;
};

export async function createShowroom(data: CreateShowroomFormValues): Promise<ShowroomAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email;
  
  try {
    const parsed = createShowroomSchema.safeParse(data);

    if (!parsed.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(parsed.error.flatten().fieldErrors).map(([key, value]) => [key, value?.[0] || ""])
      );
      logError(`createShowroom -> Validation error: ${parsed.error} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    // Check if code already exists
    const existingShowroom = await prisma.showroom.findUnique({
      where: { code: parsed.data.code }
    });

    if (existingShowroom) {
      return {
        status: "ERROR",
        message: "A showroom with this code already exists",
        fieldErrors: { code: "Code already exists" }
      };
    }

    const showroom = await prisma.showroom.create({
      data: {
        ...parsed.data,
        // Convert empty strings to null for optional fields
        address2: parsed.data.address2 || null,
        postalCode: parsed.data.postalCode || null,
        email: parsed.data.email || null,
        program: parsed.data.program || null,
      }
    });

    revalidatePath("/showrooms");
    logInfo(`AUDIT: Showroom created successfully by ${userEmail}. Showroom ID: ${showroom.id}, Code: ${showroom.code}`);

    return {
      status: "SUCCESS",
      message: "Showroom created successfully",
      data: showroom
    };
  } catch (error) {
    logError(`Error creating showroom by ${userEmail}:`, error);
    return {
      status: "ERROR",
      message: error instanceof Error ? error.message : "Failed to create showroom"
    };
  }
}

export async function updateShowroom(data: UpdateShowroomFormValues): Promise<ShowroomAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email;
  
  try {
    const parsed = updateShowroomSchema.safeParse(data);

    if (!parsed.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(parsed.error.flatten().fieldErrors).map(([key, value]) => [key, value?.[0] || ""])
      );
      logError(`updateShowroom -> Validation error: ${parsed.error} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    const { id, ...updateData } = parsed.data;

    // Check if showroom exists
    const existingShowroom = await prisma.showroom.findUnique({
      where: { id }
    });

    if (!existingShowroom) {
      return {
        status: "ERROR",
        message: "Showroom not found"
      };
    }

    // Check if code is being changed and if new code already exists
    if (updateData.code !== existingShowroom.code) {
      const codeExists = await prisma.showroom.findUnique({
        where: { code: updateData.code }
      });

      if (codeExists) {
        return {
          status: "ERROR",
          message: "A showroom with this code already exists",
          fieldErrors: { code: "Code already exists" }
        };
      }
    }

    const showroom = await prisma.showroom.update({
      where: { id },
      data: {
        ...updateData,
        // Convert empty strings to null for optional fields
        address2: updateData.address2 || null,
        postalCode: updateData.postalCode || null,
        email: updateData.email || null,
        program: updateData.program || null,
      }
    });

    revalidatePath("/showrooms");
    revalidatePath(`/showrooms/edit/${id}`);
    logInfo(`AUDIT: Showroom updated successfully by ${userEmail}. Showroom ID: ${showroom.id}, Code: ${showroom.code}`);

    return {
      status: "SUCCESS",
      message: "Showroom updated successfully",
      data: showroom
    };
  } catch (error) {
    logError(`Error updating showroom by ${userEmail}:`, error);
    return {
      status: "ERROR",
      message: error instanceof Error ? error.message : "Failed to update showroom"
    };
  }
}

export async function deleteShowroom(data: DeleteShowroomFormValues): Promise<ShowroomAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email;
  
  try {
    const parsed = deleteShowroomSchema.safeParse(data);

    if (!parsed.success) {
      logError(`deleteShowroom -> Validation error: ${parsed.error} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Invalid showroom ID"
      };
    }

    const { id } = parsed.data;

    // Check if showroom exists
    const existingShowroom = await prisma.showroom.findUnique({
      where: { id },
      include: {
        returns: { take: 1 },
        orders: { take: 1 },
        serviceRequests: { take: 1 }
      }
    });

    if (!existingShowroom) {
      return {
        status: "ERROR",
        message: "Showroom not found"
      };
    }

    // Check if showroom has related records
    const hasRelatedRecords = existingShowroom.returns.length > 0 || 
                             existingShowroom.orders.length > 0 || 
                             existingShowroom.serviceRequests.length > 0;

    if (hasRelatedRecords) {
      return {
        status: "ERROR",
        message: "Cannot delete showroom with existing orders, returns, or service requests. Please deactivate instead."
      };
    }

    await prisma.showroom.delete({
      where: { id }
    });

    revalidatePath("/showrooms");
    logInfo(`AUDIT: Showroom deleted successfully by ${userEmail}. Showroom ID: ${id}, Code: ${existingShowroom.code}`);

    return {
      status: "SUCCESS",
      message: "Showroom deleted successfully"
    };
  } catch (error) {
    logError(`Error deleting showroom by ${userEmail}:`, error);
    return {
      status: "ERROR",
      message: error instanceof Error ? error.message : "Failed to delete showroom"
    };
  }
}
