import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    useCache: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: "op47vimj99.ufs.sh",
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
      },
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
        port: '',
      },
    ],
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Ignore winston and fs-related modules on the client side
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        util: false,
      };

      config.externals = config.externals || [];
      config.externals.push({
        winston: 'winston',
        'winston-daily-rotate-file': 'winston-daily-rotate-file',
        'file-stream-rotator': 'file-stream-rotator',
      });
    }
    return config;
  },
};

export default nextConfig;
