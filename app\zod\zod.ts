import { BannerPlacement } from "@/generated/prisma";
import { z } from "zod";

export const createDiscountSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(100, "Maxim limit reached"),
  description: z.string().min(3, "Description must be at least 3 characters").max(100, "Maxim limit reached"),
  type: z.enum(["PERCENTAGE", "FIXED_AMOUNT", "NEW_PRICE"]),
  value: z.coerce.number().min(1, "Value must be greater than 1"),
  active: z.boolean(),
  startDate: z.coerce.date({
    required_error: "Start date is required",
    invalid_type_error: "Invalid start date format",
  }),
  endDate: z.coerce.date({
    required_error: "End date is required",
    invalid_type_error: "Invalid end date format",
  }),
})
.refine((data) => data.endDate >= data.startDate, {
  message: "End date cannot be before start date",
  path: ["endDate"],
})
.refine((data) => {
  const now = new Date();
  return data.endDate >= now;
}, {
  message: "End date cannot be in the past",
  path: ["endDate"],
});
export type DiscountFormValues = z.infer<typeof createDiscountSchema>;




export const updateDiscountSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(3, "Name must be at least 3 characters").max(100, "Maxim limit reached"),
  description: z.string().min(3, "Description must be at least 3 characters").max(100, "Maxim limit reached"),
  type: z.enum(["PERCENTAGE", "FIXED_AMOUNT", "NEW_PRICE"]),
  value: z.coerce.number().min(1, "Value must be greater than 1"),
  active: z.boolean(),
  startDate: z.coerce.date({
    required_error: "Start date is required",
    invalid_type_error: "Invalid start date format",
  }),
  endDate: z.coerce.date({
    required_error: "End date is required",
    invalid_type_error: "Invalid end date format",
  }),
})
.refine((data) => data.endDate >= data.startDate, {
  message: "End date cannot be before start date",
  path: ["endDate"],
})
.refine((data) => {
  const now = new Date();
  return data.endDate >= now;
}, {
  message: "End date cannot be in the past",
  path: ["endDate"],
});;
export type DiscountUpdateFormValues = z.infer<typeof updateDiscountSchema>;




export const DiscountAddProductSchema = z.object({
  discountId: z.string().cuid(),
  materialNumber: z.string().length(11, "Material Number must be exactly 11 characters"),
});
export type DiscountAddProductToFormValues = z.infer<typeof DiscountAddProductSchema>;




export const deleteProductFromDiscountSchema = z.object({
  discountId: z.string().cuid(),
  productId: z.string().cuid(),
});
export type DiscountDeleteProductFormValues = z.infer<typeof deleteProductFromDiscountSchema>;



export const processCSVDiscountSchema = z.object({
  discountId: z.string().cuid(),
  materialNumbers: z.array(z.string().length(11, "Material Number must be exactly 11 characters")),
});
export type DiscountProcessCSVFormValues = z.infer<typeof processCSVDiscountSchema>;



export const DiscountDeleteAllProductsSchema = z.object({
  discountId: z.string().cuid(),
});
export type DiscountDeleteAllProductsFormValues = z.infer<typeof DiscountDeleteAllProductsSchema>;



export const ProductSearchSchema = z.object({
  materialNumber: z.string().length(11, "Material Number must be exactly 11 characters"),
});
export type ProductSearchFormValues = z.infer<typeof ProductSearchSchema>;



export const ProductDeleteAttributeSchema = z.object({
  materialNumber: z.string().length(11, "Material Number must be exactly 11 characters"),
  attributeKey: z.string().min(3,"Too short").max(30, "Maximum size is 30")
});
export type ProductDeleteAttributeSchemaFormValues = z.infer<typeof ProductDeleteAttributeSchema>;



export const ProductAddAttributeSchema = z.object({
  materialNumber: z.string().length(11, "Material Number must be exactly 11 characters"),
  key: z.string().min(1, "Attribute Key is required.").max(100, "Key too long."),
  value: z.string().min(1, "Attribute Value is required.").max(255, "Value too long."),
});

export type ProductAddAttributeFormValues = z.infer<typeof ProductAddAttributeSchema>;


// Define a schema for the array of items
export const ProductAddAttributeArraySchema = z.array(ProductAddAttributeSchema);


export const categorySearchSchema = z.object({
  //familyCode: z.string().min(4, { message: 'Family code is required and must be at least 4 characters.' }),
  familyCode: z.string().optional(),
});

export type CategorySearchInput = z.infer<typeof categorySearchSchema>;

// Add this to your existing zod.ts file
export const createBannerSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(100, "Title is too long"),
  subtitle: z.string().max(200, "Subtitle is too long").optional(),
  imageUrl: z.string()
    .min(1, "Please enter the banner image filename")
  ,
  mobileImageUrl: z.string()
    .optional()
    .or(z.literal("")),
  callToAction: z.string().max(100, "Call to action is too long").optional(),
  buttonText: z.string().max(50, "Button text is too long").optional(),
  description: z.string().max(500, "Description is too long").optional(),
  url: z.string().url("Please enter a valid URL").optional(),

  placement: z.enum(Object.values(BannerPlacement) as [string, ...string[]]),
  //placement: z.enum(["HOME", "CATEGORY", "PRODUCT", "CHECKOUT", "SIDEBAR", "HEADER", "FOOTER", "POPUP"]),
  position: z.coerce.number().int().min(0),
  textAlignment: z.enum(["LEFT", "CENTER", "RIGHT", "BOTTOM"]),
  width: z.string().max(20).optional(),
  height: z.string().max(20).optional(),
  backgroundColor: z.string().max(20).optional(),
  textColor: z.string().max(20).optional(),

  deviceTarget: z.enum(["ALL", "DESKTOP", "MOBILE", "TABLET"]),

  startDate: z.coerce.date(),
  endDate: z.coerce.date().optional(),
  isActive: z.boolean().default(true),
});

export const updateBannerSchema = createBannerSchema.partial().extend({
  id: z.string().min(1, "Banner ID is required"),
});

export type CreateBannerFormValues = z.infer<typeof createBannerSchema>;
export type UpdateBannerFormValues = z.infer<typeof updateBannerSchema>;
