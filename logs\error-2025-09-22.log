{"level":"error","message":"Failed to add phone as email in Clerk: [<PERSON><PERSON><PERSON>]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 65dc9143ce0ad818850ce912885aeda4 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:19:10"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 68a026a80a9a29a5be599d67543d3564 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:23:58"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 4318ffd6cbeb4d4be0c0624b300c9c75 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:25:51"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:01:45"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:01:48"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:06:59"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:46"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:55"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:55"}
{"level":"error","message":"updateProductPrice: Product cmfnvr01w0005fkhx5c1d87y3 not found or has no base price","timestamp":"2025-09-22 13:43:59"}
{"level":"error","message":"updateProductPrice: Product cmfnvr01w0005fkhx5c1d87y3 not found or has no base price","timestamp":"2025-09-22 13:46:03"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11000429667\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:46:06"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11002151387\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:46:38"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11002151387\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:47:25"}
