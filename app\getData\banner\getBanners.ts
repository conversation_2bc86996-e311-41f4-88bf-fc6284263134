import prisma from "@/app/utils/db";
import { Banner, BannerPlacement } from "@/generated/prisma";
import { getCurrentUser } from "../user/data";
import { logError } from "@/lib/logger";
import { z } from "zod";


export async function getBanners(): Promise<Banner[]> {
  const user = await getCurrentUser()
  const userEmail = user?.email
  try {
    const banners = await prisma.banner.findMany({
      orderBy: [
        { placement: 'asc' },
        { position: 'asc' },
        { createdAt: 'desc' }
      ]
    });
    if (!banners) {
      logError(`getBanners -> No banners found by ${userEmail}`)
      return [];
    }
    
    // Convert any Decimal values to strings for serialization
    return banners.map(banner => ({
      ...banner,
      conversionRate: banner.conversionRate ? Number(banner.conversionRate) : null,
    }));
  } catch (error) {
    logError(`getBanners -> Error fetching banners: ${error} by ${userEmail}`)
    return [];
  }
}

export async function getBannerById(id: string): Promise<Banner | null> {
  const user = await getCurrentUser()
  const userEmail = user?.email
  try {

    //validate parameter with zod
    const validatedId = z.string().cuid().safeParse(id);
    if (!validatedId.success) {
      logError(`getBannerById -> Error at parsing: ${validatedId.error} by ${userEmail}`)
      return null;
    }
    id = validatedId.data

    const banner = await prisma.banner.findUnique({
      where: { id }
    });
    
    if (!banner) {
      logError(`getBannerById -> Banner not found with ID ${id} by ${userEmail}`)
      return null;
    }
    // Convert any Decimal values to strings for serialization
    return {
      ...banner,
      conversionRate: banner.conversionRate ? Number(banner.conversionRate) : null,
    };
  } catch (error) {
    logError(`getBannerById -> Error fetching banner with ID ${id}: ${error} by ${userEmail}`)
    return null;
  }
}

export async function getActiveBanners(placement: string): Promise<Banner[]> {
  const now = new Date();
  const user = await getCurrentUser()
  const userEmail = user?.email
  try {
    //validate parameter with zod
    const validatedPlacement = z.enum(Object.values(BannerPlacement) as [string, ...string[]]).safeParse(placement);
    if (!validatedPlacement.success) {
      logError(`getActiveBanners -> Error at parsing: ${validatedPlacement.error} by ${userEmail}`)
      return [];
    }
    placement = validatedPlacement.data

    const banners = await prisma.banner.findMany({
      where: {
        placement: placement as BannerPlacement,
        isActive: true,
        startDate: { lte: now },
        OR: [
          { endDate: null },
          { endDate: { gte: now } }
        ]
      },
      orderBy: { position: 'asc' }
    });

    if (!banners) {
      logError(`getActiveBanners -> No active banners found for placement ${placement} by ${userEmail}`)
      return [];
    }
    
    // Convert any Decimal values to strings for serialization
    return banners.map(banner => ({
      ...banner,
      conversionRate: banner.conversionRate ? Number(banner.conversionRate) : null,
    }));
  } catch (error) {
    logError(`getActiveBanners -> Error fetching active banners for placement ${placement}: ${error} by ${userEmail}`)
    return [];
  }
}