"use server";

import { revalidatePath } from "next/cache";
import prisma from "../utils/db";
import { logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export type ReturnAction = {
  status: "SUCCESS" | "ERROR";
  message: string;
};


/**
 * Toggle the featured status of a product
 */
export async function toggleFeaturedProduct(materialNumber: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    // Get current product status
    const product = await prisma.product.findUnique({
      where: { Material_Number: materialNumber },
      select: { IsOnLandingPage: true, Material_Number: true }
    });
    
    if (!product) {
      logError(`toggleFeaturedProduct -> Product not found: ${materialNumber} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Product not found",
      };
    }
    
    // Count current featured products if we're trying to add a new one
    if (!product.IsOnLandingPage) {
      const featuredCount = await prisma.product.count({
        where: { IsOnLandingPage: true }
      });
      
      if (featuredCount >= 18) {
        logError(`toggleFeaturedProduct -> Maximum of 18 featured products allowed. Remove some before adding more. by ${userEmail}`)
        return {
          status: "ERROR",
          message: "Maximum of 18 featured products allowed. Remove some before adding more.",
        };
      }
    }
    
    // Toggle the featured status
    await prisma.product.update({
      where: { Material_Number: materialNumber },
      data: { 
        IsOnLandingPage: !product.IsOnLandingPage,
        updatedBy: userEmail || "system"
      },
    });
    
    revalidatePath("/featured");
    
    const action = product.IsOnLandingPage ? "removed from" : "added to";
    logInfo(`Product ${product.Material_Number} ${action} featured products by ${userEmail}`);
    
    return {
      status: "SUCCESS",
      message: `Product ${action} featured products successfully`,
    };
  } catch (error) {
    logError(`Error toggling featured product: ${error}`);
    return {
      status: "ERROR",
      message: "Failed to update featured status",
    };
  }
}