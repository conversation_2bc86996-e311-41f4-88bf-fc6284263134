{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 11:02:26"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 11:02:47"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:02:58"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:02:58"}
{"level":"info","message":"getProductAttributes -> Product \"64119382609\" has the attributes:  by <EMAIL>","timestamp":"2025-09-22 11:03:03"}
{"level":"info","message":"getProductAttributesHistory -> Product \"64119382609\" with <NAME_EMAIL>","timestamp":"2025-09-22 11:03:03"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:03:08"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:03:08"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:03:15"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:03:15"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 101598ms","timestamp":"2025-09-22 11:04:07"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 11:04:10"}
{"level":"info","message":"getProductAttributes -> Product \"64119382609\" has the attributes:  by <EMAIL>","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"getProductAttributesHistory -> Product \"64119382609\" with <NAME_EMAIL>","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 11:04:11"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 11:04:12"}
{"level":"info","message":"Image API -> Served 64119382609_1.jpg for 64119382609","timestamp":"2025-09-22 11:04:12"}
{"level":"info","message":"getProductAttributes -> Product \"64119382609\" has the attributes:  by <EMAIL>","timestamp":"2025-09-22 11:04:15"}
{"level":"info","message":"getProductAttributesHistory -> Product \"64119382609\" with <NAME_EMAIL>","timestamp":"2025-09-22 11:04:15"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:18"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:18"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:18"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:18"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:21"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:21"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:21"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:21"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:28"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:28"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:28"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-22 11:04:28"}
{"level":"info","message":"Order status change email sent:","messageId":"<<EMAIL>>","orderId":"cmfqfwwww000khx487h9p60kz","orderNumber":"ORD-2025-30513","statusChange":"completa -> anulata","timestamp":"2025-09-22 11:05:25","to":"<EMAIL>"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"completed -> delivered","timestamp":"2025-09-22 11:07:08","to":"<EMAIL>"}
{"level":"info","message":"Group created: cmfuufjh60004<NAME_EMAIL>","timestamp":"2025-09-22 11:07:24"}
{"level":"info","message":"Group updated: cmfuufjh60004<NAME_EMAIL>","timestamp":"2025-09-22 11:07:34"}
{"level":"info","message":"User status changed for user cmfnybjze0001pv122w6ebnh5 with changes: {\"isActive\":false,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-09-22 11:08:31"}
{"level":"info","message":"User cmfnybjze0001pv122w6ebnh5 details retrieved for userEmail","timestamp":"2025-09-22 11:08:45"}
{"level":"info","message":"User cmfnybjze0001pv122w6ebnh5 details retrieved for userEmail","timestamp":"2025-09-22 11:09:33"}
{"level":"info","message":"User cmfnybjze0001pv122w6ebnh5 details retrieved for userEmail","timestamp":"2025-09-22 11:11:05"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:12:06"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":false,\"smsNotifications\":true,\"pushNotifications\":false,\"emailNotifications\":false},\"to\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":false,\"emailNotifications\":false}},\"userAM\":{\"from\":null,\"to\":\"\"},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:17:56"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:17:57"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:17:57"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:18:13"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 65dc9143ce0ad818850ce912885aeda4 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:19:10"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 68a026a80a9a29a5be599d67543d3564 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:23:58"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 4318ffd6cbeb4d4be0c0624b300c9c75 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:25:51"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"phoneNumber\":{\"from\":null,\"to\":\"+40758343280\"},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:27:58"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:27:58"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:27:59"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:32:57"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:32:58"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:32:59"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:33:00"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:06"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:07"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:07"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:08"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:30"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:33"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"phoneNumber\":{\"from\":null,\"to\":\"+40758343280\"},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:35:39"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:39"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:35:41"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":false,\"emailNotifications\":false},\"to\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":false,\"emailNotifications\":true}},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:36:37"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:36:37"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:36:39"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:39:26"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":false,\"emailNotifications\":true},\"to\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":true,\"emailNotifications\":true}},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:39:32"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:39:32"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:39:34"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:40:06"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":false,\"smsNotifications\":false,\"pushNotifications\":true,\"emailNotifications\":true},\"to\":{\"newsletterOptIn\":false,\"smsNotifications\":true,\"pushNotifications\":true,\"emailNotifications\":true}},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:40:13"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:40:13"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:40:14"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:41:51"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":false,\"smsNotifications\":true,\"pushNotifications\":true,\"emailNotifications\":true},\"to\":{\"newsletterOptIn\":true,\"smsNotifications\":true,\"pushNotifications\":true,\"emailNotifications\":true}},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:41:55"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:41:55"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:41:57"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:05"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"phoneNumber\":{\"from\":\"+40758343280\",\"to\":\"+40751111146\"},\"suspensionReason\":{\"from\":null,\"to\":\"\"},\"inactiveReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-09-22 11:43:19"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:19"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:22"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:38"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:44"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:43:48"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:46:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 11:51:30"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 11:51:35"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:51:51"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:52:06"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:52:14"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 11:52:37"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 82685ms","timestamp":"2025-09-22 11:52:57"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-22 11:52:59"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 11:53:09"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 11:53:09"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 11:53:09"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 11:53:09"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 11:53:09"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 11:53:10"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 11:53:11"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 11:53:11"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 11:53:11"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 11:53:11"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 11:53:11"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:28:22"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 12:28:32"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 78861ms","timestamp":"2025-09-22 12:29:51"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 12:29:52"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 12:40:43"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 12:40:43"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 12:40:43"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 12:40:43"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 12:40:43"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 12:40:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:57:24"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 12:57:24"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:57:30"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:57:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:58:15"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:58:19"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 12:58:28"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 80072ms","timestamp":"2025-09-22 12:58:45"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 12:58:46"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 12:58:46"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:01:45"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:01:48"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 13:06:02"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-22 13:06:02"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 13:06:02"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-22 13:06:02"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-09-22 13:06:59"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 13:07:02"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-22 13:07:02"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-22 13:07:02"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-22 13:07:02"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:46"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:55"}
{"level":"error","message":"getCategoriesByFamilyCode -> No categories found for family code 1231234134143 by <EMAIL>","timestamp":"2025-09-22 13:10:55"}
{"level":"info","message":"Discount with name Test1 created <NAME_EMAIL>","timestamp":"2025-09-22 13:15:01"}
{"level":"info","message":"Starting expired discount check at 2025-09-22T10:31:11.465<NAME_EMAIL>","timestamp":"2025-09-22 13:31:11"}
{"level":"info","message":"Found 0 expired discounts to <NAME_EMAIL>","timestamp":"2025-09-22 13:31:11"}
{"level":"info","message":"Starting activate discount check at 2025-09-22T10:31:19.805<NAME_EMAIL>","timestamp":"2025-09-22 13:31:19"}
{"level":"info","message":"Found 0 discounts to <NAME_EMAIL>","timestamp":"2025-09-22 13:31:19"}
{"level":"info","message":"No discounts found that need <NAME_EMAIL>","timestamp":"2025-09-22 13:31:19"}
{"level":"info","message":"Starting expired discount check at 2025-09-22T10:34:18.324<NAME_EMAIL>","timestamp":"2025-09-22 13:34:18"}
{"level":"info","message":"Found 0 expired discounts to <NAME_EMAIL>","timestamp":"2025-09-22 13:34:18"}
{"level":"info","message":"Starting expired discount check at 2025-09-22T10:34:36.429<NAME_EMAIL>","timestamp":"2025-09-22 13:34:36"}
{"level":"info","message":"Found 0 expired discounts to <NAME_EMAIL>","timestamp":"2025-09-22 13:34:36"}
{"level":"info","message":"Starting activate discount check at 2025-09-22T10:43:39.542<NAME_EMAIL>","timestamp":"2025-09-22 13:43:39"}
{"level":"info","message":"Found 0 discounts to <NAME_EMAIL>","timestamp":"2025-09-22 13:43:39"}
{"level":"info","message":"No discounts found that need <NAME_EMAIL>","timestamp":"2025-09-22 13:43:39"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmfnvr01w0005fkhx5c1d87y3 (11000429667)","timestamp":"2025-09-22 13:43:59"}
{"level":"info","message":"Starting updateProductPrice for product cmfnvr01w0005fkhx5c1d87y3","timestamp":"2025-09-22 13:43:59"}
{"level":"error","message":"updateProductPrice: Product cmfnvr01w0005fkhx5c1d87y3 not found or has no base price","timestamp":"2025-09-22 13:43:59"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Failed","timestamp":"2025-09-22 13:43:59"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmfuyznsl0000hx3cxhjzh8xa updated <NAME_EMAIL>","timestamp":"2025-09-22 13:43:59"}
{"level":"info","message":"Starting updateProductPrice for product cmfnvr01w0005fkhx5c1d87y3","timestamp":"2025-09-22 13:46:03"}
{"level":"error","message":"updateProductPrice: Product cmfnvr01w0005fkhx5c1d87y3 not found or has no base price","timestamp":"2025-09-22 13:46:03"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmfnvr01w0005fkhx5c1d87y3 for the discountId cmfuyznsl0000hx3cxhjzh8<NAME_EMAIL>","timestamp":"2025-09-22 13:46:03"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11000429667\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:46:06"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11002151387\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:46:38"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11002151387\" has no <NAME_EMAIL>","timestamp":"2025-09-22 13:47:25"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 13:51:56"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 13:51:56"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-22 13:52:11"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 13:52:25"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 13:52:34"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-22 13:52:41"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 89753ms","timestamp":"2025-09-22 13:53:26"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 13:53:27"}
{"level":"info","message":"AUDIT: Showroom created <NAME_EMAIL>. Showroom ID: cmfv0yk3b0000hxqg11e58zhe, Code: BAN","timestamp":"2025-09-22 14:10:08"}
{"level":"info","message":"AUDIT: Showroom updated <NAME_EMAIL>. Showroom ID: cmfv0yk3b0000hxqg11e58zhe, Code: BAN","timestamp":"2025-09-22 14:10:23"}
{"level":"info","message":"AUDIT: Showroom created <NAME_EMAIL>. Showroom ID: cmfv12c2t0001hxqgttaknbh7, Code: OTP","timestamp":"2025-09-22 14:13:05"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-22 14:20:58"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 81234ms","timestamp":"2025-09-22 14:22:19"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-22 14:22:20"}
{"level":"info","message":"AUDIT: Showroom updated <NAME_EMAIL>. Showroom ID: cmfv0yk3b0000hxqg11e58zhe, Code: BAN","timestamp":"2025-09-22 14:25:08"}
{"level":"info","message":"AUDIT: Showroom updated <NAME_EMAIL>. Showroom ID: cmfv0yk3b0000hxqg11e58zhe, Code: BAN","timestamp":"2025-09-22 14:25:17"}
