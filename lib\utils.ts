import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { Decimal } from "@prisma/client/runtime/library";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const isServer = typeof window === "undefined";


export function toSafeNumber(x: unknown): number | null {
  if (x == null) return null;
  
  // Handle Prisma Decimal instance
  if (isPrismaDecimal(x)) {
    return x.toNumber();
  }
  
  // Handle string or number
  const n = Number(x);
  return Number.isNaN(n) ? null : n;
}

function isPrismaDecimal(value: unknown): value is Decimal {
  return (
    typeof value === 'object' && 
    value !== null && 
    'toNumber' in value && 
    typeof (value as Decimal).toNumber === 'function'
  );
}
