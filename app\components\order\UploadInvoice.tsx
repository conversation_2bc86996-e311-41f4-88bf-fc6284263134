"use client";

import { useState, useTransition, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { uploadInvoiceAction } from "@/app/actions/invoiceActions";
import { Upload, FileText, Loader2 } from "lucide-react";

interface UploadInvoiceProps {
  orderId: string;
  orderNumber: string;
  invoiceAM: string | null;
}

export default function UploadInvoice({ orderId, orderNumber, invoiceAM }: UploadInvoiceProps) {
  const [isPending, startTransition] = useTransition();
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if(!invoiceAM){
      toast.error("handleFileChange-Please add the invoice number first.");
      return;
    }

    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type on client side for immediate feedback
      if (selectedFile.type !== "application/pdf") {
        toast.error("Please select a PDF file.");
        return;
      }
      
      // Validate file size (10MB limit)
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast.error("File size must be less than 10MB.");
        return;
      }
      
      setFile(selectedFile);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if(!invoiceAM){
      toast.error("handleDrop-Please add the invoice number first.");
      return;
    }

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      
      // Validate file type
      if (droppedFile.type !== "application/pdf") {
        toast.error("Please select a PDF file.");
        return;
      }
      
      // Validate file size
      if (droppedFile.size > 10 * 1024 * 1024) {
        toast.error("File size must be less than 10MB.");
        return;
      }
      
      setFile(droppedFile);
    }
  };

  // Handle click on drop zone to open file selector
  const handleDropZoneClick = () => {
    if(!invoiceAM){
      toast.error("Please add the invoice number first.");
      return;
    }
    fileInputRef.current?.click();
  };

  const handleUpload = () => {
    if (!file) {
      toast.error("Please select a PDF file to upload.");
      return;
    }

    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("orderId", orderId);

        const response = await uploadInvoiceAction(formData);

        if (response.status === "SUCCESS") {
          toast.success(response.message);
          // Reset file input and close dialog
          setFile(null);
          setIsOpen(false);
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
        } else {
          toast.error(response.message);
        }
      } catch (error) {
        toast.error("An unexpected error occurred while uploading the invoice.");
        console.error("Upload error:", error);
      }
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Upload className="mr-2 h-4 w-4" />
          Upload Invoice
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Invoice for Order {orderNumber}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          {/* File Drop Zone */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
              dragActive
                ? "border-primary bg-primary/5"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={handleDropZoneClick}
          >
            <div className="flex flex-col items-center gap-2">
              <Upload className="h-8 w-8 text-gray-400" />
              <div className="text-sm text-gray-600">
                <span className="font-medium">Click to upload</span> or drag and drop
              </div>
              <div className="text-xs text-gray-500">
                PDF files only, max 10MB
              </div>
            </div>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf,application/pdf"
            onChange={handleFileChange}
            className="hidden"
          />

          {/* Selected File Display */}
          {file && (
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <FileText className="h-5 w-5 text-red-600" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {file.name}
                </div>
                <div className="text-xs text-gray-500">
                  {formatFileSize(file.size)}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setFile(null);
                  if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                  }
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </Button>
            </div>
          )}

          {/* Upload Button */}
          <Button
            onClick={handleUpload}
            disabled={isPending || !file}
            className="w-full"
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading Invoice...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Invoice
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}


// "use client";

// import { useState, useTransition } from "react";
// import { Button } from "@/components/ui/button";
// import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
// import { toast } from "sonner";
// import { uploadInvoiceAction } from "@/app/actions/invoiceActions";
// import { Upload, FileText, Loader2 } from "lucide-react";

// interface UploadInvoiceProps {
//   orderId: string;
//   orderNumber: string;
//   invoiceAM: string | null;
// }

// export default function UploadInvoice({ orderId, orderNumber, invoiceAM }: UploadInvoiceProps) {
//   const [isPending, startTransition] = useTransition();
//   const [file, setFile] = useState<File | null>(null);
//   const [dragActive, setDragActive] = useState(false);
//   const [isOpen, setIsOpen] = useState(false);

//   const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {

//     if(!invoiceAM){
//       toast.error("handleFileChange-Please add the invoice number first.");
//       return;
//     }

//     const selectedFile = e.target.files?.[0];
//     if (selectedFile) {
//       // Validate file type on client side for immediate feedback
//       if (selectedFile.type !== "application/pdf") {
//         toast.error("Please select a PDF file.");
//         return;
//       }
      
//       // Validate file size (10MB limit)
//       if (selectedFile.size > 10 * 1024 * 1024) {
//         toast.error("File size must be less than 10MB.");
//         return;
//       }
      
//       setFile(selectedFile);
//     }
//   };

//   const handleDrag = (e: React.DragEvent) => {
//     e.preventDefault();
//     e.stopPropagation();
//     if (e.type === "dragenter" || e.type === "dragover") {
//       setDragActive(true);
//     } else if (e.type === "dragleave") {
//       setDragActive(false);
//     }
//   };

//   const handleDrop = (e: React.DragEvent) => {
//     e.preventDefault();
//     e.stopPropagation();
//     setDragActive(false);

//     if (e.dataTransfer.files && e.dataTransfer.files[0]) {
//       const droppedFile = e.dataTransfer.files[0];
      
//       // Validate file type
//       if (droppedFile.type !== "application/pdf") {
//         toast.error("Please select a PDF file.");
//         return;
//       }
      
//       // Validate file size
//       if (droppedFile.size > 10 * 1024 * 1024) {
//         toast.error("File size must be less than 10MB.");
//         return;
//       }
      
//       setFile(droppedFile);
//     }
//   };

//   const handleUpload = () => {

//     if (!file) {
//       toast.error("Please select a PDF file to upload.");
//       return;
//     }

//     startTransition(async () => {
//       try {
//         // Option 1: Use Server Action (current implementation)
//         const formData = new FormData();
//         formData.append("file", file);
//         formData.append("orderId", orderId);

//         const response = await uploadInvoiceAction(formData);

//         if (response.status === "SUCCESS") {
//           toast.success(response.message);
//           // Reset file input and close dialog
//           setFile(null);
//           setIsOpen(false);
//           const fileInput = document.getElementById(`invoice-file-input-${orderId}`) as HTMLInputElement;
//           if (fileInput) {
//             fileInput.value = '';
//           }
//         } else {
//           toast.error(response.message);
//         }
//       } catch (error) {
//         toast.error("An unexpected error occurred while uploading the invoice.");
//         console.error("Upload error:", error);
//       }
//     });
//   };

//   // Alternative method using API endpoint with token
//   const handleUploadViaAPI = async () => {
//     if (!file) {
//       toast.error("Please select a PDF file to upload.");
//       return;
//     }

//     try {
//       const formData = new FormData();
//       formData.append("file", file);

//       const response = await fetch(`/api/orders/${orderId}/upload-invoice`, {
//         method: "POST",
//         body: formData,
//       });

//       const result = await response.json();

//       if (response.ok && result.success) {
//         toast.success(result.message);
//         // Reset file input
//         setFile(null);
//         const fileInput = document.getElementById(`invoice-file-input-${orderId}`) as HTMLInputElement;
//         if (fileInput) {
//           fileInput.value = '';
//         }
//       } else {
//         toast.error(result.error || "Failed to upload invoice");
//       }
//     } catch (error) {
//       toast.error("An unexpected error occurred while uploading the invoice.");
//       console.error("API Upload error:", error);
//     }
//   };

//   const formatFileSize = (bytes: number) => {
//     if (bytes === 0) return '0 Bytes';
//     const k = 1024;
//     const sizes = ['Bytes', 'KB', 'MB'];
//     const i = Math.floor(Math.log(bytes) / Math.log(k));
//     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
//   };

//   return (
//     <Dialog open={isOpen} onOpenChange={setIsOpen}>
//       <DialogTrigger asChild>
//         <Button variant="outline">
//           <Upload className="mr-2 h-4 w-4" />
//           Upload Invoice
//         </Button>
//       </DialogTrigger>
//       <DialogContent className="sm:max-w-md">
//         <DialogHeader>
//           <DialogTitle>Upload Invoice for Order {orderNumber}</DialogTitle>
//         </DialogHeader>
//         <div className="flex flex-col gap-4">
//           {/* File Drop Zone */}
//           <div
//             className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
//               dragActive
//                 ? "border-primary bg-primary/5"
//                 : "border-gray-300 hover:border-gray-400"
//             }`}
//             onDragEnter={handleDrag}
//             onDragLeave={handleDrag}
//             onDragOver={handleDrag}
//             onDrop={handleDrop}
//           >
//             <div className="flex flex-col items-center gap-2">
//               <Upload className="h-8 w-8 text-gray-400" />
//               <div className="text-sm text-gray-600">
//                 <span className="font-medium">Click to upload</span> or drag and drop
//               </div>
//               <div className="text-xs text-gray-500">
//                 PDF files only, max 10MB
//               </div>
//             </div>

//             <input
//               id={`invoice-file-input-${orderId}`}
//               type="file"
//               accept=".pdf,application/pdf"
//               onChange={handleFileChange}
//               className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
//             />
//           </div>

//           {/* Selected File Display */}
//           {file && (
//             <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
//               <FileText className="h-5 w-5 text-red-600" />
//               <div className="flex-1 min-w-0">
//                 <div className="text-sm font-medium text-gray-900 truncate">
//                   {file.name}
//                 </div>
//                 <div className="text-xs text-gray-500">
//                   {formatFileSize(file.size)}
//                 </div>
//               </div>
//               <Button
//                 variant="ghost"
//                 size="sm"
//                 onClick={() => setFile(null)}
//                 className="text-gray-400 hover:text-gray-600"
//               >
//                 ×
//               </Button>
//             </div>
//           )}

//           {/* Upload Button */}
//           <Button
//             onClick={handleUpload}
//             disabled={isPending || !file}
//             className="w-full"
//           >
//             {isPending ? (
//               <>
//                 <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                 Uploading Invoice...
//               </>
//             ) : (
//               <>
//                 <Upload className="mr-2 h-4 w-4" />
//                 Upload Invoice
//               </>
//             )}
//           </Button>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// }
