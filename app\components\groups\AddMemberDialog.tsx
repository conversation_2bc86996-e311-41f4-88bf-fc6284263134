"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

import { Search, UserPlus, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { addUserToGroup } from "@/app/actions/userActions";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  profileImage: string | null;
  department: string | null;
  jobTitle: string | null;
}

interface AddMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  groupId: string;
  groupName: string;
  onMemberAdded: () => void;
}

export default function AddMemberDialog({ 
  open, 
  onOpenChange, 
  groupId, 
  groupName, 
  onMemberAdded 
}: AddMemberDialogProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [addingUserId, setAddingUserId] = useState<string | null>(null);

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/groups/${groupId}/available-users`);
      if (!response.ok) {
        toast.error('Failed to fetch users');
      }
      const availableUsers = await response.json();
      setUsers(availableUsers);
      setFilteredUsers(availableUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      toast.error("Failed to load available users");
    } finally {
      setLoading(false);
    }
  }, [groupId]);

  useEffect(() => {
    if (open) {
      loadUsers();
    }
  }, [open, groupId, loadUsers]);

  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredUsers(users);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = users.filter(user =>
        user.firstName.toLowerCase().includes(query) ||
        user.lastName.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        (user.department && user.department.toLowerCase().includes(query)) ||
        (user.jobTitle && user.jobTitle.toLowerCase().includes(query))
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery, users]);

  const handleAddUser = async (userId: string) => {
    setAddingUserId(userId);
    try {
      const result = await addUserToGroup({ userId, groupId });
      if (result.status === "SUCCESS") {
        toast.success("User added to group successfully");
        onMemberAdded();
        // Remove the user from the list since they're now in the group
        setUsers(prev => prev.filter(user => user.id !== userId));
        setFilteredUsers(prev => prev.filter(user => user.id !== userId));
      } else {
        toast.error(result.message || "Failed to add user to group");
      }
    } catch (error) {
      console.error("Error adding user to group:", error);
      toast.error("Failed to add user to group");
    } finally {
      setAddingUserId(null);
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "administAB":
        return "destructive";
      case "moderatorAB":
        return "default";
      case "fourLvlAdminAB":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "administAB":
        return "Administrator";
      case "moderatorAB":
        return "Moderator";
      case "angajatAB":
        return "Employee";
      case "fourLvlAdminAB":
        return "L4 Admin";
      case "fourLvlInregistratAB":
        return "L4 User";
      default:
        return "User";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add Member to {groupName}
          </DialogTitle>
          <DialogDescription>
            Select users to add to this access group. Only active users who are not already members are shown.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users by name, email, department, or job title..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Users List */}
          <div className="h-[400px] border rounded-md overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading users...</span>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery ? "No users found matching your search." : "No available users to add."}
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {filteredUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.profileImage || undefined} alt={`${user.firstName} ${user.lastName}`} />
                        <AvatarFallback>
                          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">{user.firstName} {user.lastName}</p>
                          <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
                            {getRoleDisplayName(user.role)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                        {(user.department || user.jobTitle) && (
                          <p className="text-xs text-muted-foreground">
                            {[user.department, user.jobTitle].filter(Boolean).join(" • ")}
                          </p>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleAddUser(user.id)}
                      disabled={addingUserId === user.id}
                    >
                      {addingUserId === user.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <UserPlus className="h-4 w-4 mr-1" />
                          Add
                        </>
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
