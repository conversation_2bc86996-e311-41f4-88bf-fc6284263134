import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ProductPriceHistory } from "@/generated/prisma";

interface PriceHistoryProps {
  priceHistory: ProductPriceHistory[];
}

export default function PriceHistoryComponent({ priceHistory }: PriceHistoryProps) {
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>Price History</CardTitle>
      </CardHeader>
      <CardContent>
        {priceHistory.length === 0 ? (
          <p>No price history available for this product.</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Old Base Price</TableHead>
                <TableHead>New Base Price</TableHead>
                <TableHead>Old Final Price</TableHead>
                <TableHead>New Final Price</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Changed By</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {priceHistory.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{new Date(record.createdAt).toLocaleString()}</TableCell>
                  <TableCell>{record.oldPretAM?.toString() || "N/A"}</TableCell>
                  <TableCell>{record.newPretAM?.toString() || "N/A"}</TableCell>
                  <TableCell>{record.oldFinalPrice?.toString() || "N/A"}</TableCell>
                  <TableCell>{record.newFinalPrice?.toString() || "N/A"}</TableCell>
                  <TableCell>{record.reason || "N/A"}</TableCell>
                  <TableCell>{record.source || "N/A"}</TableCell>
                  <TableCell>{record.createdBy || "System"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}