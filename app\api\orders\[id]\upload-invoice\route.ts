import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import fs from "fs";
import path from "path";
import { FILE_UPLOAD_LIMITS, NETWORK_PATHS } from "@/config/constants";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {

  const INVOICE_STORAGE = process.env.INVOICE_STORAGE;
  if(!INVOICE_STORAGE){
    logError(`API uploadInvoice -> INVOICE_STORAGE_PATH not configured`);
    return NextResponse.json(
      { error: "Storage path not configured" },
      { status: 500 }
    );
  }

  try {
    // Authentication check
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user and check permissions
    const user = await prisma.user.findFirst({
      where: { externalId: userId },
      select: { 
        email: true, 
        role: true,
        isActive: true,
        isSuspended: true
      }
    });

    if (!user || (user.role !== 'administAB' && user.role !== 'moderatorAB') || !user.isActive || user.isSuspended) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const paramsObject = await params;
    const orderId = paramsObject.id;

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;

    // Validate inputs
    if (!file) {
      logError(`API uploadInvoice -> No file provided for order ${orderId} by ${user.email}`);
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!FILE_UPLOAD_LIMITS.INVOICE.ALLOWED_TYPES.includes(file.type)) {
      logError(`API uploadInvoice -> Invalid file type: ${file.type} for order ${orderId} by ${user.email}`);
      return NextResponse.json(
        { error: "Only PDF files are allowed" },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > FILE_UPLOAD_LIMITS.INVOICE.MAX_SIZE) {
      logError(`API uploadInvoice -> File too large: ${file.size} bytes for order ${orderId} by ${user.email}`);
      return NextResponse.json(
        { error: "File size must be less than 10MB" },
        { status: 400 }
      );
    }

    // Get order details
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: { 
        id: true, 
        orderNumber: true,
        createdAt: true 
      }
    });

    if (!order) {
      logError(`API uploadInvoice -> Order not found: ${orderId} by ${user.email}`);
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      );
    }

    // Create filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `invoice_${order.orderNumber}_${timestamp}.pdf`;
    
    // Create year/month subdirectory structure
    const uploadDate = new Date();
    const year = uploadDate.getFullYear().toString();
    const month = (uploadDate.getMonth() + 1).toString().padStart(2, "0");
    
    const targetDir = path.join(NETWORK_PATHS.INVOICE_STORAGE, year, month);
    const targetPath = path.join(targetDir, filename);

    // Ensure directory exists
    try {
      await fs.promises.mkdir(targetDir, { recursive: true });
    } catch (dirError) {
      logError(`API uploadInvoice -> Failed to create directory ${targetDir}: ${dirError} by ${user.email}`);
      return NextResponse.json(
        { error: "Failed to create upload directory" },
        { status: 500 }
      );
    }

    // Convert file to buffer and save
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      await fs.promises.writeFile(targetPath, buffer);
      
      logInfo(`API uploadInvoice -> Successfully uploaded invoice for order ${order.orderNumber} to ${targetPath} by ${user.email}`);
      
      // Optionally update the order record with the file path
      await prisma.order.update({
        where: { id: orderId },
        data: { 
          // Add invoiceFilePath field to Order model if needed
          // invoiceFilePath: targetPath 
        }
      });

      return NextResponse.json({
        success: true,
        message: `Invoice uploaded successfully for order ${order.orderNumber}`,
        data: { 
          filename, 
          path: targetPath,
          orderNumber: order.orderNumber
        }
      });

    } catch (fileError) {
      logError(`API uploadInvoice -> Failed to save file: ${fileError} by ${user.email}`);
      return NextResponse.json(
        { error: "Failed to save file" },
        { status: 500 }
      );
    }

  } catch (error) {
    logError(`API uploadInvoice -> Unexpected error: ${error}`);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
