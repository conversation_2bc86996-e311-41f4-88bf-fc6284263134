import Link from "next/link";
import { getOrders } from "@/app/getData/order/getOrders";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { OrderStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";

export default async function OrderDashboard() {
  // Get the 5 most recent orders
  const { orders: recentOrders } = await getOrders({ perPage: 5 });

  // Count orders by status
  const { orders: pendingOrders } = await getOrders({ perPage: 100, status: OrderStatus.plasata });
  const pendingCount = pendingOrders.length;

  // Helper function to get status badge color
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.plasata:
        return "bg-blue-100 text-blue-800";
      case OrderStatus.completa:
        return "bg-green-100 text-green-800";
      case OrderStatus.anulata:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Orders</CardTitle>
        <CardDescription>
          Manage customer orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800">Pending Orders</h3>
            <p className="text-2xl font-bold text-blue-900">{pendingCount}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-800">Recent Orders</h3>
            <p className="text-2xl font-bold text-gray-900">{recentOrders.length}</p>
          </div>
        </div>

        <h3 className="text-sm font-medium mb-3">Recent Orders</h3>
        {recentOrders.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No orders found</p>
        ) : (
          <div className="space-y-3">
            {recentOrders.map((order) => (
              <div
                key={order.id}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{order.orderNumber}</p>
                  <p className="text-sm text-gray-500">
                    {formatDate(new Date(order.createdAt))}
                  </p>
                </div>
                <Badge className={getStatusColor(order.orderStatus)}>
                  {order.orderStatus.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href="/orders">View All Orders</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}