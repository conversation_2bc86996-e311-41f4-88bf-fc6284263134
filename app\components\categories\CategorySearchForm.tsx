
// components/CategorySearchForm.tsx
'use client'; // Mark this as a client component

import Form from 'next/form'
import { Input } from '@/components/ui/input';
import { SubmitButton } from '../buttons';



interface CategorySearchFormProps {
  initialFamilyCode?: string; // Prop to pre-fill the input from searchParams
}

export function CategorySearchForm({ initialFamilyCode }: CategorySearchFormProps) {
  return (
      <Form action="" className="flex gap-4">
          <Input
            id="familyCode"
            name="familyCode" 
            defaultValue={initialFamilyCode || ''} // Use defaultValue for uncontrolled input
            className='w-full'
            placeholder="Family Code Search "
          />
        <SubmitButton /> {/* The button automatically interacts with the parent Form */}
      </Form>
  );
}

//WORKS but uses the classic form and react-hook-form
// 'use client'; // Mark this as a client component

// import { useRouter } from 'next/navigation'; // Import useRouter from next/navigation for client-side navigation
// import { useForm } from 'react-hook-form';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { z } from 'zod';
// import { useEffect } from 'react'; // To synchronize input with URL on initial load

// // Define the Zod schema for client-side validation
// const categorySearchSchema = z.object({
//   // familyCode can be optional initially or an empty string, but if present, must meet min length
//   familyCode: z.string().min(1, { message: 'Family code is required.' }).optional().or(z.literal('')),
// });

// type CategorySearchInput = z.infer<typeof categorySearchSchema>;

// interface CategorySearchFormClientProps {
//   initialFamilyCode?: string; // Prop to pre-fill the input from the URL's searchParams
// }

// export function CategorySearchForm({ initialFamilyCode }: CategorySearchFormClientProps) {
//   const router = useRouter(); // Initialize the router
//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting },
//     setValue, // Function to programmatically set form values
//   } = useForm<CategorySearchInput>({
//     resolver: zodResolver(categorySearchSchema),
//     defaultValues: {
//       familyCode: initialFamilyCode || '', // Set default value from prop for initial render
//     },
//   });

//   // useEffect to ensure the form input field is updated if the initialFamilyCode prop changes
//   // This handles cases where the user navigates between search results that change the searchParam
//   useEffect(() => {
//     setValue('familyCode', initialFamilyCode || '');
//   }, [initialFamilyCode, setValue]);

//   const onSubmit = (data: CategorySearchInput) => {
//     const query = new URLSearchParams();
//     if (data.familyCode && data.familyCode.trim() !== '') {
//       query.set('familyCode', data.familyCode.trim()); // Add familyCode to query params
//     }
//     // Navigate to the current path with updated search parameters.
//     // This will trigger a re-render of the server component (app/search/page.tsx).
//     router.push(`/searchCategory?${query.toString()}`);
//   };

//   return (
//     <div className="max-w-2xl mx-auto p-4 bg-white shadow-md rounded-lg">
//       <h2 className="text-2xl font-bold mb-4">Search Category by Family Code</h2>
//       <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
//         <div>
//           <label htmlFor="familyCode" className="block text-sm font-medium text-gray-700">
//             Family Code Search Term:
//           </label>
//           <input
//             type="text"
//             id="familyCode"
//             {...register('familyCode')} // react-hook-form handles input attributes
//             className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
//             placeholder="e.g., 000"
//           />
//           {errors.familyCode && (
//             <p className="mt-1 text-sm text-red-600">{errors.familyCode.message}</p>
//           )}
//         </div>
//         <button
//           type="submit"
//           disabled={isSubmitting} // Disable button while form is submitting/navigating
//           className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
//         >
//           {isSubmitting ? 'Searching...' : 'Search'}
//         </button>
//       </form>
//     </div>
//   );
// }


// IT WORKS but it uses the server action - POST method
// 'use client';

// import { searchCategoryServerAction } from '@/app/getData/categories/getData';
// import { initialSearchFormState, SearchFormState } from '@/app/types/Types';
// import { useActionState } from 'react';
// import Form from 'next/form'
// import { useFormStatus } from 'react-dom';
// import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';


// function SubmitButton() {
//   const { pending } = useFormStatus(); // Hook to get form submission status
//   return (
//     <Button
//       type="submit"
//       aria-disabled={pending}
//       disabled={pending}
//       //className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
//     >
//       {pending ? 'Searching...' : 'Search'}
//     </Button>
//   );
// }

// export function CategorySearchForm() {
//   const [state, formAction] = useActionState<SearchFormState, FormData>(
//     searchCategoryServerAction,
//     initialSearchFormState
//   );

//   return (
//     <div className="p-4">
//       <h2 className="text-2xl font-bold mb-4">Category</h2>
//       {/* Use the next/form component */}
//       <Form action={formAction} className="flex gap-2 mb-4">
//           <Input
//             placeholder="Family Code Search Term:"
//             id="familyCode"
//             name="familyCode" // Crucial: input 'name' must match formData.get('name') in server action
//             className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
//           />
//           {state.errors?.familyCode && (
//             <p className="mt-1 text-sm text-red-600">
//               {state.errors.familyCode.join(', ')} {/* Display all errors */}
//             </p>
//           )}
//         <SubmitButton /> {/* Use the separate submit button component */}
//       </Form>

//       {state.message && (
//         <p className={`mt-4 text-center ${state.categories && state.categories.length > 0 ? 'text-green-600' : 'text-red-600'}`}>
//           {state.message}
//         </p>
//       )}

//       {state.categories && state.categories.length > 0 && (
//         <div className="mt-6 p-4 border border-gray-200 rounded-md bg-gray-50">
//           <h3 className="text-xl font-semibold mb-4">Matching Category Details:</h3>
//           <div className="space-y-4">
//             {state.categories.map((category) => (
//               <div key={category.level3.id} className="p-3 border border-gray-300 rounded-md bg-white shadow-sm">
//                 <p>
//                   <strong>Level 1:</strong> {category.level1.name} ({category.level1.nameRO || 'N/A'})
//                   {category.level1.imageUrl && <img src={category.level1.imageUrl} alt={category.level1.name} className="inline-block h-6 w-6 ml-2" />}
//                 </p>
//                 <p className="ml-4">
//                   <strong>Level 2:</strong> {category.level2.name} ({category.level2.nameRO || 'N/A'})
//                   {category.level2.imageUrl && <img src={category.level2.imageUrl} alt={category.level2.name} className="inline-block h-6 w-6 ml-2" />}
//                 </p>
//                 <p className="ml-4">
//                   <strong>Level 3:</strong> {category.level3.name} ({category.level3.nameRO || 'N/A'})
//                   {category.level3.imageUrl && <img src={category.level3.imageUrl} alt={category.level3.name} className="inline-block h-6 w-6 ml-2" />}
//                 </p>
//                 <p className="ml-4">
//                   <strong>Family Code:</strong> {category.level3.familyCode}
//                 </p>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }