import { NextRequest, NextResponse } from "next/server";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { logError, logInfo } from "@/lib/logger";
import { z } from "zod";
import { getImagesForMaterials } from "@/lib/imageCache";

// Validation schema for batch requests
const batchImageRequestSchema = z.object({
  materialNumbers: z.array(z.string().min(1)).min(1).max(100), // Limit to 100 materials per batch
  includeMetadata: z.boolean().default(false),
  onlyValid: z.boolean().default(true)
});

interface BatchImageResult {
  materialNumber: string;
  totalImages: number;
  images: Array<{
    fileName: string;
    sequenceNumber: number;
    fileExtension: string;
    fileSize?: number;
    url: string;
  }>;
  error?: string;
}

/**
 * Batch API endpoint for retrieving images for multiple material numbers
 * This is much more efficient than making individual API calls
 * 
 * POST /api/images/batch
 * Body: {
 *   materialNumbers: string[],
 *   includeMetadata?: boolean,
 *   onlyValid?: boolean
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const actor = await requireAdminOrModerator();
    const userEmail = actor.email;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = batchImageRequestSchema.parse(body);
    const { materialNumbers, includeMetadata, onlyValid } = validatedData;

    logInfo(`Batch image request for ${materialNumbers.length} materials by ${userEmail}`);

    // Get images from the cached index
    const imagesByMaterial = await getImagesForMaterials(materialNumbers);

    // Build response for each requested material number
    const results: BatchImageResult[] = materialNumbers.map(materialNumber => {
      const images = imagesByMaterial[materialNumber] || [];

      return {
        materialNumber,
        totalImages: images.length,
        images: images.map(img => ({
          fileName: img.fileName,
          sequenceNumber: img.sequenceNumber,
          fileExtension: img.fileExtension,
          ...(includeMetadata && {
            fileSize: img.fileSize,
          }),
          url: `/api/images/${materialNumber}${img.sequenceNumber > 0 ? `?sequence=${img.sequenceNumber}` : ""}`
        }))
      };
    });

    // Calculate summary statistics
    const totalImages = results.reduce((sum, result) => sum + result.totalImages, 0);
    const materialsWithImages = results.filter(result => result.totalImages > 0).length;
    const materialsWithoutImages = results.length - materialsWithImages;

    const response = {
      success: true,
      summary: {
        requestedMaterials: materialNumbers.length,
        materialsWithImages,
        materialsWithoutImages,
        totalImages,
        includeMetadata,
        onlyValid
      },
      results
    };

    logInfo(`Batch image request completed: ${totalImages} images for ${materialsWithImages}/${materialNumbers.length} materials by ${userEmail}`);

    return NextResponse.json(response);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors
        },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("Unauthorized")) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    logError(`Batch image API -> Unexpected error:`, error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
