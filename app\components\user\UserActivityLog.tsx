"use client";

import { useState } from "react";
import { format } from "date-fns";
import { 
  LogIn, 
  LogOut, 
  Settings, 
  UserCog, 
  Shield, 
  Alert<PERSON><PERSON>gle, 
  Check, 
  X, 
  Clock 
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Prisma } from "@/generated/prisma";

interface UserAuditLog {
  id: string;
  userId: string;
  action: string;
  details: Prisma.JsonValue | null;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
  performedBy: string | null;
}

interface UserActivityLogProps {
  userId: string;
  auditLogs: UserAuditLog[];
}

export default function UserActivityLog({ auditLogs }: UserActivityLogProps) {
  const [viewAll, setViewAll] = useState(false);
  
  // Function to get icon for activity type
  const getActivityIcon = (action: string) => {
    switch (action) {
      case "LOGIN":
        return <LogIn className="h-4 w-4 text-green-500" />;
      case "LOGOUT":
        return <LogOut className="h-4 w-4 text-gray-500" />;
      case "PROFILE_UPDATE":
        return <UserCog className="h-4 w-4 text-blue-500" />;
      case "SETTINGS_CHANGE":
        return <Settings className="h-4 w-4 text-blue-500" />;
      case "PERMISSION_CHANGE":
        return <Shield className="h-4 w-4 text-purple-500" />;
      case "FAILED_LOGIN":
        return <X className="h-4 w-4 text-red-500" />;
      case "PASSWORD_RESET":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "PASSWORD_CHANGE":
        return <Check className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  
  // Function to get formatted activity description
  const getActivityDescription = (log: UserAuditLog) => {
    switch (log.action) {
      case "LOGIN":
        return "User logged in";
      case "LOGOUT":
        return "User logged out";
      case "PROFILE_UPDATE":
        return "Profile information updated";
      case "SETTINGS_CHANGE":
        return "Account settings changed";
      case "PERMISSION_CHANGE":
        return "Permissions modified";
      case "FAILED_LOGIN":
        return "Failed login attempt";
      case "PASSWORD_RESET":
        return "Password reset requested";
      case "PASSWORD_CHANGE":
        return "Password changed";
      default:
        return log.action.replace(/_/g, " ").toLowerCase();
    }
  };
  
  // Display only first 5 logs unless viewAll is true
  const displayLogs = viewAll ? auditLogs : auditLogs.slice(0, 5);
  
  return (
    <div>
      {displayLogs.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No activity recorded for this user
        </div>
      ) : (
        <div className="space-y-4">
          {displayLogs.map((log) => (
            <div key={log.id} className="flex items-start gap-3 pb-4 border-b last:border-0">
              <div className="mt-1 p-2 bg-gray-100 rounded-full">
                {getActivityIcon(log.action)}
              </div>
              <div className="flex-1">
                <div className="font-medium">{getActivityDescription(log)}</div>
                {log.details && (
                  <div className="text-sm text-gray-500 mt-1">
                    {typeof log.details === 'string' ? log.details : JSON.stringify(log.details)}
                  </div>
                )}
                <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                  <span>{format(new Date(log.createdAt), "PPpp")}</span>
                  {log.ipAddress && (
                    <span>IP: {log.ipAddress}</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {auditLogs.length > 5 && (
        <div className="mt-4 text-center">
          <Button 
            variant="outline" 
            onClick={() => setViewAll(!viewAll)}
          >
            {viewAll ? "Show Less" : `View All (${auditLogs.length})`}
          </Button>
        </div>
      )}
    </div>
  );
}