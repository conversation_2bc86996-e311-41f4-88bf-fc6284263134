import { Metadata } from "next";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getServiceRequests } from "@/app/getData/service/data";
import ServiceList from "@/app/components/service/ServiceList";

export const metadata: Metadata = {
  title: "Service Management",
  description: "View and manage customer service requests",
};

interface ServicesPageProps {
  searchParams: Promise<{
    page?: string;
    perPage?: string;
    sort?: string;
    order?: string;
    query?: string;
    status?: string;
  }>;
}

export default async function ServicesRoute({ searchParams }: ServicesPageProps) {
  await requireAdminOrModerator();

  // Await the searchParams Promise
  const params = await searchParams;

  const page = Number(params.page) || 1;
  const perPage = Number(params.perPage) || 10;
  const sort = params.sort || "createdAt";

  const { services, totalServices, totalPages } = await getServiceRequests({
    query: params.query,
    status: params.status as any,
    page,
    perPage,
    sort,
    order: (params.order === "asc" || params.order === "desc") ? params.order : "desc",
  });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Service Management</h1>
      </div>

      <ServiceList
        services={services}
        totalServices={totalServices}
        totalPages={totalPages}
        currentPage={page}
        perPage={perPage}
      />
    </div>
  );
}