import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { logError, logInfo } from "@/lib/logger";
import { buildImageIndex, getImagesForMaterial, ImageFile } from "@/lib/imageCache";

/**
 * Dynamic API route to serve product images based on material number
 * Supports multiple naming patterns:
 * - {materialNumber}.{ext} (base image)
 * - {materialNumber}_1.{ext}, {materialNumber}_2.{ext}, etc. (additional images)
 * 
 * Usage:
 * - GET /api/images/ABC123 - Returns the base image (ABC123.jpg, ABC123.png, etc.)
 * - GET /api/images/ABC123?sequence=1 - Returns the first additional image (ABC123_1.jpg)
 * - GET /api/images/ABC123?all=true - Returns JSON array of all available images
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ materialNumber: string }> }
) {
  try {
    const { materialNumber } = await params;
    const { searchParams } = new URL(request.url);
    const sequence = searchParams.get("sequence");
    const returnAll = searchParams.get("all") === "true";

    // Validate material number
    if (!materialNumber || typeof materialNumber !== "string") {
      return NextResponse.json(
        { error: "Valid material number is required" },
        { status: 400 }
      );
    }

    // Get images from the cached index
    const images = await getImagesForMaterial(materialNumber);

    // Add debug logging for troubleshooting
    logInfo(`Image API -> Looking for material ${materialNumber}, found ${images.length} images`);
    if (images.length === 0) {
      // Get the full index to check for similar material numbers
      const imageIndex = await buildImageIndex();
      const allMaterialNumbers = Object.keys(imageIndex);
      const caseInsensitiveMatches = allMaterialNumbers.filter(mn =>
        mn.toLowerCase() === materialNumber.toLowerCase() && mn !== materialNumber
      );
      if (caseInsensitiveMatches.length > 0) {
        logInfo(`Image API -> No exact match for ${materialNumber}, but found case-insensitive matches: ${caseInsensitiveMatches.join(', ')}`);
      }
    }

    if (returnAll) {
      // Return JSON array of all available images
      const imageList = images.map(img => ({
        fileName: img.fileName,
        sequence: img.sequenceNumber,
        fileSize: img.fileSize,
        fileExtension: img.fileExtension,
        url: `/api/images/${materialNumber}${img.sequenceNumber > 0 ? `?sequence=${img.sequenceNumber}` : ""}`
      }));

      return NextResponse.json({
        materialNumber,
        totalImages: imageList.length,
        images: imageList
      });
    }

    // Find the specific image to serve
    let targetImage: ImageFile | null = null;

    if (sequence !== null) {
      // Looking for a specific sequence number
      const sequenceNum = parseInt(sequence, 10);
      if (isNaN(sequenceNum) || sequenceNum < 0) {
        return NextResponse.json(
          { error: "Invalid sequence number" },
          { status: 400 }
        );
      }
      targetImage = images.find(img => img.sequenceNumber === sequenceNum) || null;
    } else {
      // Looking for the base image (sequence 0), fallback to first available image
      targetImage = images.find(img => img.sequenceNumber === 0) ||
                   (images.length > 0 ? images.sort((a, b) => a.sequenceNumber - b.sequenceNumber)[0] : null);
    }

    if (!targetImage) {
      // Serve the default product image instead of returning 404
      try {
        const defaultImagePath = path.join(process.cwd(), 'public', 'productDefault.jpg');
        const fileBuffer = await fs.readFile(defaultImagePath);

        return new NextResponse(fileBuffer as BodyInit, {
          status: 200,
          headers: {
            'Content-Type': 'image/jpeg',
            'Cache-Control': 'public, max-age=31536000, immutable',
          },
        });
      } catch (defaultImageError) {
        logError(`Error serving default image: ${defaultImageError}`);

        // If default image also fails, return JSON 404
        const availableSequences = images
          .map(img => img.sequenceNumber)
          .sort((a, b) => a - b);

        return NextResponse.json(
          {
            error: "Image not found and default image unavailable",
            materialNumber,
            requestedSequence: sequence ? parseInt(sequence, 10) : 0,
            availableSequences
          },
          { status: 404 }
        );
      }
    }

    // Serve the image file
    try {
      const fileBuffer = await fs.readFile(targetImage.fullPath);
      const fileExtension = targetImage.fileExtension.toLowerCase();
      
      // Determine content type
      let contentType = "application/octet-stream";
      switch (fileExtension) {
        case ".jpg":
        case ".jpeg":
          contentType = "image/jpeg";
          break;
        case ".png":
          contentType = "image/png";
          break;
        case ".webp":
          contentType = "image/webp";
          break;
        case ".gif":
          contentType = "image/gif";
          break;
      }

      // Set cache headers for better performance
      const headers = new Headers({
        "Content-Type": contentType,
        //"Cache-Control": "public, max-age=31536000, immutable", // Cache for 1 year
        "Content-Length": fileBuffer.length.toString(),
      });

      logInfo(`Image API -> Served ${targetImage.fileName} for ${materialNumber}`);

      return new NextResponse(fileBuffer as BodyInit, {
        status: 200,
        headers,
      });

    } catch (fileError) {
      logError(`Image API -> Failed to read file ${targetImage.fullPath}: ${fileError}`);
      return NextResponse.json(
        { error: "Failed to read image file" },
        { status: 500 }
      );
    }

  } catch (error) {
    logError(`Image API -> Unexpected error:`, error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}


