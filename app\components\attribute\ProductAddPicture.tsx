"use client"

import { useState, useEffect, useTransition, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  ImageIcon,
  Loader2,
  RefreshCw,
  Database,
  Eye
} from "lucide-react";
import Image from "next/image";

interface ProductAddPicturesProps {
  materialNumber: string;
}

interface IndexedImage {
  fileName: string;
  sequenceNumber: number;
  fileExtension: string;
  fileSize?: number;
  url: string;
}

interface BatchImageResult {
  materialNumber: string;
  totalImages: number;
  images: IndexedImage[];
  error?: string;
}

export default function ProductAddPictures({ materialNumber }: ProductAddPicturesProps) {
  const [indexedImages, setIndexedImages] = useState<IndexedImage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isPending] = useTransition();

  const loadIndexedImages = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/images/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          materialNumbers: [materialNumber],
          includeMetadata: true,
          onlyValid: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.results.length > 0) {
        const result: BatchImageResult = data.results[0];
        if (result.error) {
          toast.error(`Error loading images: ${result.error}`);
        } else {
          setIndexedImages(result.images);
          setLastUpdated(new Date());
          if (result.totalImages > 0) {
            toast.success(`Found ${result.totalImages} cached images for ${materialNumber}`);
          }
        }
      }
    } catch (error) {
      console.error("Error loading indexed images:", error);
      toast.error("Failed to load cached images");
    } finally {
      setIsLoading(false);
    }
  }, [materialNumber]);

  // Load indexed images on component mount
  useEffect(() => {
    loadIndexedImages();
  }, [materialNumber, loadIndexedImages]);

  const handleRefreshImages = async () => {
    await loadIndexedImages();
  };

  const handleRefreshCache = async () => {
    setIsRefreshing(true);
    try {
      toast.info("Refreshing image cache... This will take a moment.");

      // Force refresh by calling the API again
      await loadIndexedImages();

      toast.success("Image cache refreshed successfully!");
    } catch (error) {
      console.error("Error refreshing cache:", error);
      toast.error("Failed to refresh image cache");
    } finally {
      setIsRefreshing(false);
    }
  };

  const openImageInNewTab = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 justify-between">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Pictures for <span className="text-red-500">{materialNumber}</span>
            <Badge variant="outline">{indexedImages.length} cached</Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshImages}
              disabled={isLoading || isPending}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshCache}
              disabled={isRefreshing || isPending}
            >
              {isRefreshing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Refreshing...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Refresh Cache
                </>
              )}
            </Button>

          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">


        {/* Cached Images */}
        <div>
          <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
            Cached Images from File System
            <Badge variant="outline">{indexedImages.length}</Badge>
            {lastUpdated && (
              <span className="text-xs text-gray-500">
                Cached: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </h3>
          
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading indexed images...
            </div>
          ) : indexedImages.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {indexedImages.map((image, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border">
                    <Image
                      src={image.url}
                      alt={`${materialNumber} - ${image.fileName}`}
                      width={200}
                      height={200}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/productDefault.jpg';
                      }}
                    />
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openImageInNewTab(image.url)}
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="mt-1 space-y-1">
                    <p className="text-xs font-medium truncate">{image.fileName}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Badge variant="outline" className="text-xs">
                        #{image.sequenceNumber}
                      </Badge>
                      {image.fileSize && (
                        <span>{formatFileSize(image.fileSize)}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No cached images found for this material number</p>
              <p className="text-sm">Click Refresh Cache to reload from file system</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
