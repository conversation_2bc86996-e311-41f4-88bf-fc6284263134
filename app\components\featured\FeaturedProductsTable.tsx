"use client";

import { useState, useTransition } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { toggleFeaturedProduct } from "@/app/actions/featuredActions";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { Eye, Star } from "lucide-react";
import { formatCurrency } from "@/app/utils/formatters";



interface FeaturedProductsTableProps {
  products: {
    id: string;
    Material_Number: string;
    Description_Local: string | null;
    ImageUrl: string[] | null;
    FinalPrice: number | null;
  }[];
}

export default function FeaturedProductsTable({ products }: FeaturedProductsTableProps) {
  const [isPending, startTransition] = useTransition();
  const [pendingProduct, setPendingProduct] = useState<string | null>(null);
  const [failedImages] = useState<Set<string>>(new Set());

  const deleteFeatured = (product: string) => {
    setPendingProduct(product);
    startTransition(async () => {
      try {
        const result = await toggleFeaturedProduct(product);
        if (result.status === "SUCCESS") {
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch {
        toast.error("An error occurred while updating the product");
      } finally {
        setPendingProduct(null);
      }
    });
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Image</TableHead>
            <TableHead>Material Number</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Price</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell>
                <div className="relative h-12 w-12">
                  <Image
                    src={failedImages.has(product.Material_Number) ? '/productDefault.jpg' : `/api/images/${product.Material_Number}`}
                    alt={product.Description_Local || "Product image"}
                    fill
                    className="object-cover rounded-md"
                  />
                </div>
              </TableCell>
              <TableCell>{product.Material_Number}</TableCell>
              <TableCell className="max-w-xs truncate">
                {product.Description_Local || "No description"}
              </TableCell>
              <TableCell>
                {product.FinalPrice 
                  ? formatCurrency(parseFloat(product.FinalPrice.toString())) 
                  : "N/A"}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Link href={`/product?materialNumber=${product.Material_Number}`}>
                    <Button variant="outline" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => deleteFeatured(product.Material_Number)}
                    disabled={isPending && pendingProduct === product.Material_Number}
                  >
                    {isPending && pendingProduct === product.Material_Number ? (
                      "Removing..."
                    ) : (
                      <>
                        <Star className="h-4 w-4 mr-1" /> Remove
                      </>
                    )}
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}