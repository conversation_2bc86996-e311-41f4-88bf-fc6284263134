"use server";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getShowrooms } from "@/app/getData/showroom/data";
import { Plus, Edit, MapPin, Phone, Mail, Building2 } from "lucide-react";
import ShowroomDeleteButton from "@/app/components/showroom/ShowroomDeleteButton";

interface ShowroomsPageProps {
  searchParams: Promise<{
    includeInactive?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  }>;
}

export default async function ShowroomsPage({ searchParams }: ShowroomsPageProps) {
  await requireAdminOrModerator();

  const params = await searchParams;
  const includeInactive = params.includeInactive === "true";
  const search = params.search;
  const sortBy = (params.sortBy as any) || "sortOrder";
  const sortOrder = (params.sortOrder as "asc" | "desc") || "asc";

  const { showrooms, totalCount, activeCount, inactiveCount } = await getShowrooms({
    includeInactive,
    search,
    sortBy,
    sortOrder,
  });

  return (
    <div className="container mx-auto py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Showrooms Management</h1>
          <p className="text-muted-foreground mt-1">
            Manage showroom locations and information
          </p>
        </div>
        <Link href="/showrooms/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Showroom
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Showrooms</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Badge variant="default" className="h-4 w-4 p-0"></Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive</CardTitle>
            <Badge variant="secondary" className="h-4 w-4 p-0"></Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{inactiveCount}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Link
              href={`/showrooms?${new URLSearchParams({
                ...params,
                includeInactive: (!includeInactive).toString()
              }).toString()}`}
            >
              <Button variant={includeInactive ? "default" : "outline"} size="sm">
                {includeInactive ? "Show Active Only" : "Include Inactive"}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Showrooms Table */}
      <Card>
        <CardHeader>
          <CardTitle>Showrooms List</CardTitle>
          <CardDescription>
            {showrooms.length} showroom{showrooms.length !== 1 ? "s" : ""} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {showrooms.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No showrooms</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new showroom.
              </p>
              <div className="mt-6">
                <Link href="/showrooms/create">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Showroom
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sort Order</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {showrooms.map((showroom) => (
                    <TableRow key={showroom.id}>
                      <TableCell className="font-medium">
                        <Badge variant="outline">{showroom.code}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{showroom.name}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-start gap-1">
                          <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div className="text-sm">
                            <div>{showroom.address1}</div>
                            {showroom.address2 && <div>{showroom.address2}</div>}
                            <div>{showroom.city}, {showroom.county}</div>
                            {showroom.postalCode && <div>{showroom.postalCode}</div>}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-1 text-sm">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            {showroom.phone}
                          </div>
                          {showroom.email && (
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3 text-muted-foreground" />
                              {showroom.email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={showroom.isActive ? "default" : "secondary"}>
                          {showroom.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {showroom.sortOrder !== null ? showroom.sortOrder : "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Link href={`/showrooms/edit/${showroom.id}`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <ShowroomDeleteButton showroomId={showroom.id} showroomName={showroom.name} />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}