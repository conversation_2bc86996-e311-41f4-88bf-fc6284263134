"use client";

import {
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Bar,
} from "recharts";

interface UserRegistrationData {
  date: string;
  registrations: number;
}

interface UserRegistrationChartProps {
  data: UserRegistrationData[];
}

const aggregateData = (data: UserRegistrationData[]) => {
  const aggregated = data.reduce((acc: Record<string, number>, curr) => {
    if (acc[curr.date]) {
      acc[curr.date] += curr.registrations;
    } else {
      acc[curr.date] = curr.registrations;
    }
    return acc;
  }, {});

  return Object.keys(aggregated).map((date) => ({
    date,
    registrations: aggregated[date],
  }));
};

export function UserRegistrationChart({ data }: UserRegistrationChartProps) {
  const processedData = aggregateData(data);
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={processedData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar
          dataKey="registrations"
          fill="#10b981"
          name="New Users"
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
