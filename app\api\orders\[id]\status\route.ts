import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/app/utils/db";
import { sendOrderStatusChangeEmail, OrderStatusChangeEmailData } from "@/lib/email";

export async function PATCH(
  request: NextRequest,
 context: { params: Promise<{ id: string }> } 
) {
  try {
    const { userId } = await auth();
    const { id } = await context.params;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { status, note, invoiceAM } = await request.json();
    
    // Validate status
    const validStatuses = ["plasata", "completa", "anulata"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status" },
        { status: 400 }
      );
    }
    
    // Get user email for audit
    const user = await prisma.user.findFirst({
      where: { externalId: userId },
      select: { email: true }
    });
    
    const updatedBy = user?.email || userId;
    
    // Get current order status first
    const currentOrder = await prisma.order.findUnique({
      where: { id },
      select: { orderStatus: true }
    });

    // Update order in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.order.update({
        where: { id },
        data: {
          orderStatus: status,
          updatedBy,
          invoiceAM: invoiceAM || null
        }
      });

      // Create status history entry
      await tx.orderStatusHistory.create({
        data: {
          orderId: id,
          orderStatus: status,
          previousOrderStatus: currentOrder?.orderStatus,
          notes: note,
          changedBy: updatedBy
        }
      });

      return updatedOrder;
    });

    // Send email notification to customer (async, don't wait for it)
    if (result && currentOrder?.orderStatus !== status) {
      // Get order details for email
      const orderForEmail = await prisma.order.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          },
          orderItems: {
            include: {
              product: {
                select: {
                  Description_Local: true,
                  Material_Number: true
                }
              }
            }
          }
        }
      });

      if (orderForEmail && orderForEmail.user) {
        const emailData: OrderStatusChangeEmailData = {
          orderId: orderForEmail.id,
          orderNumber: orderForEmail.orderNumber,
          customerName: `${orderForEmail.user.firstName} ${orderForEmail.user.lastName}`,
          customerEmail: orderForEmail.user.email,
          newStatus: status,
          previousStatus: currentOrder?.orderStatus || 'unknown',
          adminNotes: note,
          invoiceAM: invoiceAM,
          items: orderForEmail.orderItems.map(item => ({
            name: item.product.Description_Local || 'Unknown Product',
            quantity: item.quantity,
            price: Number(item.price),
            code: item.product.Material_Number
          })),
          total: Number(orderForEmail.totalAmount)
        };

        // Send email asynchronously (don't await to avoid blocking the response)
        sendOrderStatusChangeEmail(emailData).catch(error => {
          console.error('Failed to send order status change email:', error);
        });
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error updating order status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}