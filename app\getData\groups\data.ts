import prisma from "@/app/utils/db";
import { $Enums } from "@/generated/prisma";
import { logError } from "@/lib/logger";

//GroupByIdInterface for the getGroupById return function
export interface GroupByIdInterface {
    id: string;
    name: string;
    description: string | null;
    permissions: string[];
    createdAt: Date;
    updatedAt: Date;
    createdBy: string | null;
    updatedBy: string | null;

    users: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        profileImage: string;
        userAM: string | null;
        role: $Enums.Rol;
    }[];
    _count: {
        users: number;
    };
}

export async function getAllGroups() {
  try {
    const groups = await prisma.userGroup.findMany({
      include: {
        _count: {
          select: {
            users: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    return groups;
  } catch (error) {
    logError("Error fetching groups:", error);
    throw new Error("Failed to fetch groups");
  }
}

export async function getGroupById(id: string): Promise<GroupByIdInterface | null> {
  try {
    const group = await prisma.userGroup.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            role: true,
            profileImage: true,
            userAM: true,
          }
        },
        _count: {
          select: {
            users: true
          }
        }
      }
    });
    
    return group;
  } catch (error) {
    logError(`Error fetching group ${id}:`, error);
    throw new Error("Failed to fetch group");
  }
}

export async function getAvailableGroupsForUser(userId: string) {
  try {
    const groups = await prisma.userGroup.findMany({
      where: {
        users: {
          none: {
            id: userId
          }
        }
      },
      select: {
        id: true,
        name: true,
        description: true
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    return groups;
  } catch (error) {
    logError(`Error fetching available groups for user ${userId}:`, error);
    throw new Error("Failed to fetch available groups");
  }
}
