import { z } from "zod";

// Base showroom schema with all fields
const showroomBaseSchema = z.object({
  code: z.string()
    .min(1, "Code is required")
    .max(10, "Code cannot exceed 10 characters")
    .regex(/^[A-Z0-9]+$/, "Code must contain only uppercase letters and numbers"),
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name cannot exceed 100 characters"),
  address1: z.string()
    .min(5, "Address line 1 must be at least 5 characters")
    .max(200, "Address line 1 cannot exceed 200 characters"),
  address2: z.string()
    .max(200, "Address line 2 cannot exceed 200 characters")
    .optional()
    .or(z.literal("")),
  city: z.string()
    .min(2, "City must be at least 2 characters")
    .max(100, "City cannot exceed 100 characters"),
  county: z.string()
    .min(2, "County must be at least 2 characters")
    .max(100, "County cannot exceed 100 characters"),
  postalCode: z.string()
    .max(20, "Postal code cannot exceed 20 characters")
    .optional()
    .or(z.literal("")),
  phone: z.string()
    .min(10, "Phone number must be at least 10 characters")
    .max(20, "Phone number cannot exceed 20 characters")
    .regex(/^[\d\s\-\+\(\)]+$/, "Phone number contains invalid characters"),
  email: z.string()
    .email("Invalid email address")
    .max(100, "Email cannot exceed 100 characters")
    .optional()
    .or(z.literal("")),
  program: z.string()
    .max(500, "Program cannot exceed 500 characters")
    .optional()
    .or(z.literal("")),
  isActive: z.boolean(),
  sortOrder: z.coerce.number()
    .int("Sort order must be a whole number")
    .min(0, "Sort order must be 0 or greater")
    .optional(),
});

// Schema for creating a new showroom
export const createShowroomSchema = showroomBaseSchema;

// Schema for updating an existing showroom
export const updateShowroomSchema = showroomBaseSchema.extend({
  id: z.string().cuid("Invalid showroom ID"),
});

// Schema for deleting a showroom
export const deleteShowroomSchema = z.object({
  id: z.string().cuid("Invalid showroom ID"),
});

// Export types for use in components and actions
export type CreateShowroomFormValues = z.infer<typeof createShowroomSchema>;
export type UpdateShowroomFormValues = z.infer<typeof updateShowroomSchema>;
export type DeleteShowroomFormValues = z.infer<typeof deleteShowroomSchema>;

// Type for showroom data from database (includes id and timestamps)
export type ShowroomData = {
  id: string;
  code: string;
  name: string;
  address1: string;
  address2: string | null;
  city: string;
  county: string;
  postalCode: string | null;
  phone: string;
  email: string | null;
  program: string | null;
  isActive: boolean;
  sortOrder: number | null;
  createdAt?: Date;
  updatedAt?: Date;
};
