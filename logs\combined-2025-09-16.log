{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-16 09:16:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 09:17:18"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\80225A072C8","timestamp":"2025-09-16 09:17:25"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\36112462648-2462649","timestamp":"2025-09-16 09:17:25"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\11617799873","timestamp":"2025-09-16 09:17:25"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\82729402896","timestamp":"2025-09-16 09:17:25"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\51719626587","timestamp":"2025-09-16 09:17:25"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\11122334336","timestamp":"2025-09-16 09:17:25"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 09:17:30"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 09:18:07"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 09:18:07"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 09:47:13"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 09:47:13"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 09:55:00"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 09:55:00"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:56:45"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:56:45"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:56:45"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:56:46"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:56:56"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:56:57"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:56:57"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:56:57"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:57:13"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:57:13"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:57:14"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:57:14"}
{"level":"info","message":"discoverProductImages -> Found 0 images for 11531743192 by <EMAIL>","timestamp":"2025-09-16 09:59:45"}
{"level":"error","message":"syncProductImages -> Unexpected error:: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\nStack: Error: Route /product used \"revalidatePath /product\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\n    at revalidate (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2771:41)\n    at revalidatePath (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_d8cfa959._.js:2742:12)\n    at syncProductImages (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:515:157)\n    at async getProductDetails (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3368:13)\n    at async ProductRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\[root-of-the-server]__84c08971._.js:3544:30)","timestamp":"2025-09-16 09:59:46"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 09:59:51"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 09:59:51"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:21:27"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:21:27"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 10:21:28"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 10:21:28"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 10:46:40"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 10:46:40"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:46:40"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:46:40"}
{"level":"error","message":"startPartialImageIndex -> Unexpected error:: Cannot read properties of undefined (reading 'create')\nStack: TypeError: Cannot read properties of undefined (reading 'create')\n    at startPartialImageIndex (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__3482d955._.js:9532:169)\n    at async POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__3482d955._.js:9906:24)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:46:51"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:46:53"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:46:53"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:47:43"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:47:43"}
{"level":"error","message":"startPartialImageIndex -> Unexpected error:: Cannot read properties of undefined (reading 'create')\nStack: TypeError: Cannot read properties of undefined (reading 'create')\n    at startPartialImageIndex (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__3482d955._.js:9532:169)\n    at async POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__3482d955._.js:9906:24)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:47:48"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:47:49"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:47:49"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:49:28"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:49:28"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:49:28"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:49:28"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:51:07"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:51:07"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 10:51:07"}
{"level":"error","message":"Batch image API -> Unexpected error:: Cannot read properties of undefined (reading 'findMany')\nStack: TypeError: Cannot read properties of undefined (reading 'findMany')\n    at POST (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\[root-of-the-server]__57061717._.js:9453:175)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\compiled\\next-server\\app-route-turbo.runtime.dev.js:26:41338)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:2440:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\base-server.js:899:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","timestamp":"2025-09-16 10:51:07"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 10:51:09"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 10:51:09"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:09:40"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 11:09:40"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 11:09:41"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 11:09:41"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:10:24"}
{"level":"info","message":"Image index built successfully: 10313 images for 8780 materials in 81870ms","timestamp":"2025-09-16 11:11:02"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:11:02"}
{"level":"info","message":"Batch image request completed: 3 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:11:02"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-16 11:11:04"}
{"level":"info","message":"Image API -> Served 36115A24E75_3.jpg for 36115A24E75","timestamp":"2025-09-16 11:11:04"}
{"level":"info","message":"Image API -> Served 36115A24E75_2.jpg for 36115A24E75","timestamp":"2025-09-16 11:11:04"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:11:40"}
{"level":"info","message":"Batch image request completed: 3 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:11:40"}
{"level":"info","message":"Image API -> Served 36115A279A2.jpg for 36115A279A2","timestamp":"2025-09-16 11:11:41"}
{"level":"info","message":"Image API -> Served 36115A279A2_2.jpg for 36115A279A2","timestamp":"2025-09-16 11:11:41"}
{"level":"info","message":"Image API -> Served 36115A279A2_3.jpg for 36115A279A2","timestamp":"2025-09-16 11:11:41"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:17:42"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:17:42"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 11:22:59"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:22:59"}
{"level":"info","message":"Batch image request completed: 0 images for 0/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:22:59"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:22:59"}
{"level":"info","message":"Batch image request completed: 0 images for 0/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:22:59"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 11:22:59"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:23:55"}
{"level":"info","message":"Batch image request completed: 0 images for 0/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:23:55"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:24:41"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 11:24:41"}
{"level":"info","message":"Image index built successfully: 10313 images for 8780 materials in 81303ms","timestamp":"2025-09-16 11:26:03"}
{"level":"info","message":"Batch image request completed: 3 images for 1/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:26:03"}
{"level":"info","message":"Image API -> Served 36115A279F9_3.jpg for 36115A279F9","timestamp":"2025-09-16 11:26:03"}
{"level":"info","message":"Image API -> Served 36115A279F9.jpg for 36115A279F9","timestamp":"2025-09-16 11:26:03"}
{"level":"info","message":"Image API -> Served 36115A279F9_2.jpg for 36115A279F9","timestamp":"2025-09-16 11:26:03"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:26:25"}
{"level":"info","message":"Batch image request completed: 3 images for 1/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:26:25"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:26:56"}
{"level":"info","message":"Batch image request completed: 2 images for 1/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:26:56"}
{"level":"info","message":"Image API -> Served 61615A43585.jpg for 61615A43585","timestamp":"2025-09-16 11:26:58"}
{"level":"info","message":"Image API -> Served 61615A43585_2.jpg for 61615A43585","timestamp":"2025-09-16 11:26:58"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:14"}
{"level":"info","message":"Batch image request completed: 13 images for 7/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:14"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:28"}
{"level":"info","message":"Batch image request completed: 13 images for 7/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:28"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:30"}
{"level":"info","message":"Batch image request completed: 13 images for 7/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:30"}
{"level":"info","message":"Batch image request for 24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:39"}
{"level":"info","message":"Batch image request completed: 13 images for 7/24 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:39"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:53"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:28:53"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 11:29:12"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 11:29:12"}
{"level":"info","message":"Image API -> Served 84212289718.jpg for 84212289718","timestamp":"2025-09-16 11:29:13"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:34:31"}
{"level":"info","message":"Product 84212289718 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:34:42"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:34:42"}
{"level":"info","message":"Product 84102449887 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:34:50"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:34:50"}
{"level":"info","message":"Product 83122468609 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:34:56"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:34:57"}
{"level":"info","message":"Product 83122288898 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:35:08"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:35:08"}
{"level":"info","message":"Product 83120427832 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:35:15"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:35:15"}
{"level":"info","message":"Product 82792456534 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:35:27"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:35:27"}
{"level":"info","message":"Product 82732223388 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:35:37"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:35:37"}
{"level":"info","message":"Product 82729405860 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:35:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:35:45"}
{"level":"info","message":"Product 36117847084 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:36:01"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:36:01"}
{"level":"info","message":"Product 36116876636 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:36:22"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:36:22"}
{"level":"info","message":"Product 18107504168 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:36:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:36:34"}
{"level":"info","message":"Product 13720400734 added to featured <NAME_EMAIL>","timestamp":"2025-09-16 11:36:41"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 11:36:41"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:00:34"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:00:34"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:00:34"}
{"level":"info","message":"Image index built successfully: 10313 images for 8780 materials in 107932ms","timestamp":"2025-09-16 12:02:22"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:23"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:23"}
{"level":"info","message":"Image API -> Served 36116876636_2.jpg for 36116876636","timestamp":"2025-09-16 12:02:24"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 12:02:31"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:35"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:35"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:35"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:35"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:37"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:37"}
{"level":"info","message":"Image API -> Served 83122288898.jpg for 83122288898","timestamp":"2025-09-16 12:02:37"}
{"level":"info","message":"Image API -> Served 83122288898_2.jpg for 83122288898","timestamp":"2025-09-16 12:02:38"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 12:02:45"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:49"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:49"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:49"}
{"level":"info","message":"Batch image request completed: 2 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:49"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:52"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:02:52"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:05:24"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:05:24"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:05:25"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:05:25"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:05:25"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:05:25"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:05:25"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:05:26"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:06:00"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:06:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:06:18"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:06:18"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:06:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:06:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:06:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:06:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:06:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:06:52"}
{"level":"info","message":"Image index built successfully: 10313 images for 8780 materials in 92691ms","timestamp":"2025-09-16 12:06:57"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:06:57"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:07:07"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:07:07"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\Filename must be a valid image file (jpg, jpeg, png, webp)","timestamp":"2025-09-16 12:09:34"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\Filename must be a valid image file (jpg, jpeg, png, webp)","timestamp":"2025-09-16 12:09:34"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:10:05"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:10:05"}
{"level":"info","message":"AUDIT: Banner created successfully","timestamp":"2025-09-16 12:10:11"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:10:14"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:13:08"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:13:19"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:13:19"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:13:28"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:13:28"}
{"level":"info","message":"Image API -> Served 82729402896.jpg for 82729402896","timestamp":"2025-09-16 12:13:34"}
{"level":"info","message":"Image API -> Served 11122334336.jpg for 11122334336","timestamp":"2025-09-16 12:14:16"}
{"level":"info","message":"Image API -> Served 82729402896.jpg for 82729402896","timestamp":"2025-09-16 12:14:16"}
{"level":"info","message":"Image API -> Served 80225A072C8.jpg for 80225A072C8","timestamp":"2025-09-16 12:14:16"}
{"level":"info","message":"Image API -> Served 51719626587.jpg for 51719626587","timestamp":"2025-09-16 12:14:17"}
{"level":"info","message":"Image API -> Served 36112462648-2462649.jpg for 36112462648-2462649","timestamp":"2025-09-16 12:14:17"}
{"level":"info","message":"Image API -> Served 17123413819.jpg for 17123413819","timestamp":"2025-09-16 12:14:17"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:14:38"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:14:38"}
{"level":"info","message":"Image API -> Served 82729402896.jpg for 82729402896","timestamp":"2025-09-16 12:15:52"}
{"level":"info","message":"Image API -> Served 80452465960.jpg for 80452465960","timestamp":"2025-09-16 12:17:01"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80452465960","timestamp":"2025-09-16 12:17:01"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:18:49"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as 80452465960","timestamp":"2025-09-16 12:18:49"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:18:57"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as bmw_logo_M.webp","timestamp":"2025-09-16 12:18:57"}
{"level":"info","message":"Banner Image API -> Served bmw_logo_M.webp","timestamp":"2025-09-16 12:18:58"}
{"level":"info","message":"Image API -> Served 80452465960.jpg for 80452465960","timestamp":"2025-09-16 12:19:38"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:21:28"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as banner22.png","timestamp":"2025-09-16 12:21:28"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:21:29"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:21:34"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as banner11.png","timestamp":"2025-09-16 12:21:35"}
{"level":"info","message":"Banner Image API -> Served banner22.png","timestamp":"2025-09-16 12:21:55"}
{"level":"info","message":"Image index built successfully: 10315 images for 8782 materials in 84182ms","timestamp":"2025-09-16 12:22:53"}
{"level":"info","message":"Banner Image API -> Served banner22.png","timestamp":"2025-09-16 12:22:54"}
{"level":"info","message":"Banner Image API -> Served banner11.png","timestamp":"2025-09-16 12:22:54"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:23:25"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as default2.png","timestamp":"2025-09-16 12:23:25"}
{"level":"info","message":"Banner Image API -> Served default2.png","timestamp":"2025-09-16 12:23:25"}
{"level":"info","message":"Banner Image API -> Served default2.png","timestamp":"2025-09-16 12:23:26"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:23:32"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as default.png","timestamp":"2025-09-16 12:23:32"}
{"level":"info","message":"Banner Image API -> Served default.png","timestamp":"2025-09-16 12:23:33"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.pn","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.pn","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.p","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.p","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2.","timestamp":"2025-09-16 12:24:09"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:24:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:24:10"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:24:29"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:24:29"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:24:39"}
{"level":"info","message":"AUDIT: Banner created successfully","timestamp":"2025-09-16 12:24:58"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:51"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:51"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:51"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:51"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:53"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:53"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:57"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:26:57"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:27:37"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:27:37"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 12:28:54"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-16 12:28:57"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:28:58"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:28:58"}
{"level":"info","message":"Image index built successfully: 10318 images for 8785 materials in 93188ms","timestamp":"2025-09-16 12:29:11"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:29:11"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:29:11"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:29:11"}
{"level":"info","message":"Image API -> Served default2.png for default2","timestamp":"2025-09-16 12:29:11"}
{"level":"info","message":"Image API -> Served 83122468609.jpg for 83122468609","timestamp":"2025-09-16 12:29:12"}
{"level":"info","message":"Image API -> Served default2.png for default2","timestamp":"2025-09-16 12:29:52"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:29:53"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:30:07"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:30:07"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80225A072C8","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11122334336","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\36112462648-2462649","timestamp":"2025-09-16 12:31:15"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\default2","timestamp":"2025-09-16 12:31:16"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\17123413819","timestamp":"2025-09-16 12:31:16"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:32:56"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:32:56"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:32:57"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:36:40"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as parts6___Copy.jpg","timestamp":"2025-09-16 12:36:40"}
{"level":"info","message":"Banner Image API -> Served parts6___Copy.jpg","timestamp":"2025-09-16 12:36:41"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\parts6___Copy.jp","timestamp":"2025-09-16 12:36:44"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\parts6___Copy.j","timestamp":"2025-09-16 12:36:44"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\parts6___Copy.","timestamp":"2025-09-16 12:36:44"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\parts6___Copy","timestamp":"2025-09-16 12:36:45"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896","timestamp":"2025-09-16 12:37:11"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:37:19"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as accesories.jpg","timestamp":"2025-09-16 12:37:19"}
{"level":"info","message":"Banner Image API -> Served accesories.jpg","timestamp":"2025-09-16 12:37:20"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\accesories.jp","timestamp":"2025-09-16 12:37:22"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\accesories.j","timestamp":"2025-09-16 12:37:23"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\accesories.","timestamp":"2025-09-16 12:37:23"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\accesories","timestamp":"2025-09-16 12:37:23"}
{"level":"info","message":"Banner image file not found or already deleted: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\82729402896 by <EMAIL>","timestamp":"2025-09-16 12:37:34"}
{"level":"info","message":"Banner updated successfully: cmf5d6rq50003hx2wp8n8pg63 by <EMAIL>","timestamp":"2025-09-16 12:37:34"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873","timestamp":"2025-09-16 12:38:17"}
{"level":"info","message":"Image API -> Served 80142463110.jpg for 80142463110","timestamp":"2025-09-16 12:38:51"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\80142463110","timestamp":"2025-09-16 12:38:51"}
{"level":"info","message":"Banner image file not found or already deleted: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\11617799873 by <EMAIL>","timestamp":"2025-09-16 12:39:19"}
{"level":"info","message":"Banner updated successfully: cmf5dfm680008hx2wwr8<NAME_EMAIL>","timestamp":"2025-09-16 12:39:19"}
{"level":"info","message":"Image API -> Served 80142463110.jpg for 80142463110","timestamp":"2025-09-16 12:39:20"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:23"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:23"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:23"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:23"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:27"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:27"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:37"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:37"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:37"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:37"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:38"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:38"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:39"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:39"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:40"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 12:40:40"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:41:07"}
{"level":"info","message":"Image index built successfully: 10321 images for 8788 materials in 83029ms","timestamp":"2025-09-16 12:42:30"}
{"level":"info","message":"Image API -> Served accesories.jpg for accesories","timestamp":"2025-09-16 12:42:31"}
{"level":"info","message":"Image API -> Served accesories.jpg for accesories","timestamp":"2025-09-16 12:43:20"}
{"level":"info","message":"Image API -> Served accesories.jpg for accesories","timestamp":"2025-09-16 12:43:30"}
{"level":"error","message":"API serveBannerImage -> Banner image not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\accesories","timestamp":"2025-09-16 12:43:31"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 12:47:19"}
{"level":"info","message":"Image index built successfully: 10321 images for 8788 materials in 83758ms","timestamp":"2025-09-16 12:48:43"}
{"level":"info","message":"Image API -> Served 80225A072C8.jpg for 80225A072C8","timestamp":"2025-09-16 12:48:43"}
{"level":"info","message":"Image API -> Served 51719626587.jpg for 51719626587","timestamp":"2025-09-16 12:48:43"}
{"level":"info","message":"Image API -> Served 51719626587.jpg for 51719626587","timestamp":"2025-09-16 12:48:43"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:49:12"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as intretinere.jpg","timestamp":"2025-09-16 12:49:13"}
{"level":"info","message":"API uploadBannerImage -> Upload request <NAME_EMAIL>","timestamp":"2025-09-16 12:49:23"}
{"level":"info","message":"API uploadBannerImage -> File already exists, will be overwritten: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\intretinere.<NAME_EMAIL>","timestamp":"2025-09-16 12:49:23"}
{"level":"info","message":"API uploadBannerImage -> AUDIT: Banner image uploaded <NAME_EMAIL> as intretinere.jpg","timestamp":"2025-09-16 12:49:23"}
{"level":"info","message":"Banner image file not found or already deleted: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587 by <EMAIL>","timestamp":"2025-09-16 12:50:01"}
{"level":"info","message":"Banner image file not found or already deleted: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\51719626587 by <EMAIL>","timestamp":"2025-09-16 12:50:01"}
{"level":"info","message":"Banner updated successfully: cmf5d8byn0004hx2w9b8n1e1<NAME_EMAIL>","timestamp":"2025-09-16 12:50:01"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 13:59:21"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 13:59:21"}
{"level":"info","message":"Image index built successfully: 10322 images for 8789 materials in 93228ms","timestamp":"2025-09-16 14:00:54"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:00:54"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:49:20"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:50:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:50:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:50:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 14:50:31"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:50:49"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-16 14:50:49"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\bmw-logo-M.webp","timestamp":"2025-09-16 14:50:49"}
{"level":"error","message":"API serveImage -> File not found: \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\pictures\\PhotoProducts\\products\\11531743192\\banner11.png","timestamp":"2025-09-16 14:50:49"}
{"level":"info","message":"Image index built successfully: 10322 images for 8789 materials in 89239ms","timestamp":"2025-09-16 14:52:18"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:52:19"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:53:05"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:53:05"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:53:18"}
{"level":"info","message":"Batch image request completed: 3 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:53:18"}
{"level":"info","message":"Image API -> Served 36115A24E75_2.jpg for 36115A24E75","timestamp":"2025-09-16 14:53:18"}
{"level":"info","message":"Image API -> Served 36115A24E75_3.jpg for 36115A24E75","timestamp":"2025-09-16 14:53:19"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-16 14:53:19"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:54:51"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:54:51"}
{"level":"info","message":"Image API -> Served 85452729136_2.jpg for 85452729136","timestamp":"2025-09-16 14:54:52"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:54:59"}
{"level":"info","message":"Batch image request completed: 3 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:54:59"}
{"level":"info","message":"Image API -> Served 36112462583_2.jpg for 36112462583","timestamp":"2025-09-16 14:55:00"}
{"level":"info","message":"Image API -> Served 36112462583_1.jpg for 36112462583","timestamp":"2025-09-16 14:55:00"}
{"level":"info","message":"Image API -> Served 36112462583.jpg for 36112462583","timestamp":"2025-09-16 14:55:00"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:16"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:16"}
{"level":"info","message":"Image API -> Served 84109247916_2.jpg for 84109247916","timestamp":"2025-09-16 14:55:16"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:35"}
{"level":"info","message":"Batch image request completed: 1 images for 1/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:35"}
{"level":"info","message":"Image API -> Served 13720400734_2.jpg for 13720400734","timestamp":"2025-09-16 14:55:36"}
{"level":"info","message":"Batch image request for 1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:51"}
{"level":"info","message":"Batch image request completed: 0 images for 0/1 <NAME_EMAIL>","timestamp":"2025-09-16 14:55:51"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-16 14:56:12"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-16 14:56:16"}
{"level":"info","message":"Return cmfmihbs5000mhxxsn84mqpx7 status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-16 15:11:45"}
{"level":"info","message":"Return cmfmihbs5000mhxxsn84mqpx7 status updated from received to <NAME_EMAIL>","timestamp":"2025-09-16 15:11:54"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 15:49:11"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:30"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-09-16 16:31:31"}
