import { Metadata } from "next";
import CreateShowroomForm from "@/app/components/showroom/CreateShowroomForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export const metadata: Metadata = {
  title: "Create Showroom",
  description: "Create a new showroom location",
};

export default async function CreateShowroomPage() {
  await requireAdminOrModerator();
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        {/* <Link href="/showrooms">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Showrooms
          </Button>
        </Link> */}
        <div>
          <h1 className="text-2xl font-bold">Create New Showroom</h1>
          <p className="text-muted-foreground mt-1">
            Add a new showroom location to the system
          </p>
        </div>
      </div>
      
      <CreateShowroomForm />
    </div>
  );
}
