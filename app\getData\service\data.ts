import prisma from "@/app/utils/db";
import { ServiceStatus, Prisma } from "@/generated/prisma";
import { cache } from "react";

interface GetServiceRequestsParams {
  query?: string;
  status?: ServiceStatus;
  page?: number;
  perPage?: number;
  sort?: string;
  order?: "asc" | "desc";
}

export async function getServiceRequestById(id: string) {
  try {
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: true,
        action: {
          include: {
            orderItem: {
              include: {
                order: true,
                product: true,
              },
            },
          },
        },
        address: true,
        showroom: true,
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
        },
      },
    });

    if (!serviceRequest) return null;

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequest = {
      ...serviceRequest,
      user: {
        id: serviceRequest.user.id,
        firstName: serviceRequest.user.firstName,
        lastName: serviceRequest.user.lastName,
        email: serviceRequest.user.email,
      },
      action: serviceRequest.action ? {
        ...serviceRequest.action,
        orderItem: {
          ...serviceRequest.action.orderItem,
          price: serviceRequest.action.orderItem.price.toNumber(),
          order: {
            id: serviceRequest.action.orderItem.order.id,
            orderNumber: serviceRequest.action.orderItem.order.orderNumber,
            amount: serviceRequest.action.orderItem.order.amount.toNumber(),
            totalAmount: serviceRequest.action.orderItem.order.totalAmount.toNumber(),
          },
          product: {
            id: serviceRequest.action.orderItem.product.id,
            Material_Number: serviceRequest.action.orderItem.product.Material_Number,
            Description_Local: serviceRequest.action.orderItem.product.Description_Local,
            PretAM: serviceRequest.action.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: serviceRequest.action.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      } : null,
    };

    return serializedServiceRequest;
  } catch (error) {
    console.error("Error fetching service request:", error);
    return null;
  }
}

export const getServiceRequests = cache(async ({
  query,
  status,
  page = 1,
  perPage = 10,
  sort = "createdAt",
  order = "desc"
}: GetServiceRequestsParams = {}) => {
  try {
    // Build where clause
    const where: Prisma.ServiceRequestWhereInput = {};

    // Apply filters
    if (query) {
      where.OR = [
        { serviceNumber: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { user: {
          OR: [
            { email: { contains: query, mode: 'insensitive' } },
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
          ]
        }},
      ];
    }

    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const skip = (page - 1) * perPage;

    // Validate sort field
    const validSortFields = ['serviceNumber', 'createdAt', 'status', 'issueType'];
    const safeSort = validSortFields.includes(sort) ? sort : 'createdAt';

    // Execute query with pagination
    const [serviceRequests, totalServiceRequests] = await Promise.all([
      prisma.serviceRequest.findMany({
        where,
        orderBy: { [safeSort]: order },
        skip,
        take: perPage,
        include: {
          user: true,
          action: {
            include: {
              orderItem: {
                include: {
                  product: true,
                },
              },
            },
          },
        },
      }),
      prisma.serviceRequest.count({ where })
    ]);

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequests = serviceRequests.map(request => ({
      ...request,
      user: {
        id: request.user.id,
        firstName: request.user.firstName,
        lastName: request.user.lastName,
        email: request.user.email,
      },
      action: request.action ? {
        ...request.action,
        orderItem: {
          ...request.action.orderItem,
          price: request.action.orderItem.price.toNumber(),
          product: {
            ...request.action.orderItem.product,
            Description_Local: request.action.orderItem.product.Description_Local,
            PretAM: request.action.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: request.action.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      } : null,
    }));

    const totalPages = Math.ceil(totalServiceRequests / perPage);

    return { services: serializedServiceRequests, totalServices: totalServiceRequests, totalPages };
  } catch (error) {
    console.error("Error fetching service requests:", error);
    return { services: [], totalServices: 0, totalPages: 0 };
  }
});

export async function getServiceRequestsByUserId(userId: string) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: { userId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        action: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
          take: 1,
        },
      },
    });

    return serviceRequests;
  } catch (error) {
    console.error("Error fetching service requests for user:", error);
    return [];
  }
}
