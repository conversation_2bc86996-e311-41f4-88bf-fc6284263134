"server-only"

import { CategoryFetchResult, FoundCategoryDisplay } from "@/app/types/Types";
import prisma from "@/app/utils/db";
import { categorySearchSchema } from "@/app/zod/zod";
import { getCurrentUser } from "../user/data";
import { logError } from "@/lib/logger";

export async function getCategoriesByFamilyCode(familyCode: string | undefined): Promise<CategoryFetchResult> {
  const user = await getCurrentUser()
  const userEmail = user?.email
  // Server-side validation using Zod
  const validatedFields = categorySearchSchema.safeParse({ familyCode });

  if (!validatedFields.success) {
    logError(`getCategoriesByFamilyCode -> Error at parsing: ${validatedFields.error} by ${userEmail}`)
    return {
      categories: null,
      message: 'Invalid search parameter format.',
      errors: { familyCode: ['Invalid input format.'] },
    };
  }

  const rawFamilyCodeParam = validatedFields.data.familyCode;

    if (rawFamilyCodeParam === undefined || rawFamilyCodeParam.trim() === '' || rawFamilyCodeParam === null) {
      logError(`getCategoriesByFamilyCode -> No family code provided by ${userEmail}`)
    return {
      categories: null,
      message: 'Enter a family code to search.', // A general prompt, not an error
      errors: undefined, // No errors for this case
    };
  }

    const trimmedFamilyCode = rawFamilyCodeParam.trim();
  if (trimmedFamilyCode === '') {
    logError(`getCategoriesByFamilyCode -> Family code is required by ${userEmail}`)
    return {
      categories: null,
      message: 'Family code is required.', // This is the error message for empty submission
      errors: { familyCode: ['Family code is required.'] },
    };
  }

  const searchTerm = trimmedFamilyCode; 

  try {
    const categoryLevel3Matches = await prisma.categoryLevel3.findMany({
      where: {
        familyCode: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      },
      include: {
        level2: {
          include: {
            level1: true,
          },
        },
      },
      orderBy: {
        familyCode: 'asc',
      },
    });

    if (categoryLevel3Matches.length === 0) {
      logError(`getCategoriesByFamilyCode -> No categories found for family code ${searchTerm} by ${userEmail}`)
      return {
        categories: null,
        message: 'No categories found containing this family code.',
        errors: undefined, // No errors for this case
      };
    }

    const formattedCategories: FoundCategoryDisplay[] = categoryLevel3Matches.map(cat3 => ({
      level1: {
        id: cat3.level2.level1.id,
        name: cat3.level2.level1.name,
        nameRO: cat3.level2.level1.nameRO,
        imageUrl: cat3.level2.level1.imageUrl,
      },
      level2: {
        id: cat3.level2.id,
        name: cat3.level2.name,
        nameRO: cat3.level2.nameRO,
        imageUrl: cat3.level2.imageUrl,
      },
      level3: {
        id: cat3.id,
        name: cat3.name,
        nameRO: cat3.nameRO,
        familyCode: cat3.familyCode,
        imageUrl: cat3.imageUrl,
      },
    }));

    return {
      categories: formattedCategories,
      message: `${formattedCategories.length} categories found!`,
      errors: undefined, // No errors on successful search
    };
  } catch (error) {
    logError(`getCategoriesByFamilyCode -> Error fetching categories: ${error} by ${userEmail}`)
    return {
      categories: null,
      message: 'An unexpected error occurred during search.',
      errors: undefined, // No specific input errors for unexpected system errors
    };
  } 
}

//WORKS but uses the classic form and react-hook-form
// export async function getCategoriesByFamilyCode(familyCode: string | undefined): Promise<FoundCategoryDisplay[]> {
//   // If no familyCode is provided or it's just whitespace, return an empty array
//   if (!familyCode || familyCode.trim() === '') {
//     return [];
//   }

//   try {
//     const categoryLevel3Matches = await prisma.categoryLevel3.findMany({
//       where: {
//         familyCode: {
//           contains: familyCode.trim(), // Trim whitespace from the input for search
//           mode: 'insensitive',
//         },
//       },
//       include: {
//         level2: {
//           include: {
//             level1: true,
//           },
//         },
//       },
//       orderBy: {
//         familyCode: 'asc', // Order results for consistent display
//       },
//     });

//     const formattedCategories: FoundCategoryDisplay[] = categoryLevel3Matches.map(cat3 => ({
//       level1: {
//         id: cat3.level2.level1.id,
//         name: cat3.level2.level1.name,
//         nameRO: cat3.level2.level1.nameRO,
//         imageUrl: cat3.level2.level1.imageUrl,
//       },
//       level2: {
//         id: cat3.level2.id,
//         name: cat3.level2.name,
//         nameRO: cat3.level2.nameRO,
//         imageUrl: cat3.level2.imageUrl,
//       },
//       level3: {
//         id: cat3.id,
//         name: cat3.name,
//         nameRO: cat3.nameRO,
//         familyCode: cat3.familyCode,
//         imageUrl: cat3.imageUrl,
//       },
//     }));

//     return formattedCategories;
//   } catch (error) {
//     console.error('Error fetching categories:', error);
//     // In a real application, you might want to return an error status or re-throw
//     return [];
//   } finally {
//     // It's good practice to disconnect Prisma client in serverless environments
//     // For local development with a single process, you might manage a global prisma client.
//     await prisma.$disconnect();
//   }
// }

// export async function searchCategoryServerAction(
//   prevState: SearchFormState, // The previous state from useFormState
//   formData: FormData // The form data submitted by the <Form> component
// ): Promise<SearchFormState> {
//   const familyCode = formData.get('familyCode');

//   // Validate the input using Zod
//   const validatedFields = categorySearchSchema.safeParse({
//     familyCode,
//   });

//   // If validation fails, return the errors
//   if (!validatedFields.success) {
//     return {
//       ...prevState,
//       message: 'Validation failed.',
//       errors: validatedFields.error.flatten().fieldErrors,
//       categories: null, // Clear categories on validation error
//     };
//   }

//   const { familyCode: searchFamilyCode } = validatedFields.data;

//   try {
//     const categoryLevel3Matches = await prisma.categoryLevel3.findMany({
//       where: {
//         familyCode: {
//           contains: searchFamilyCode,
//           mode: 'insensitive',
//         },
//       },
//       include: {
//         level2: {
//           include: {
//             level1: true,
//           },
//         },
//       },
//       orderBy: {
//         familyCode: 'asc',
//       },
//     });

//     if (categoryLevel3Matches.length === 0) {
//       return {
//         ...initialSearchFormState, // Reset to initial state for a fresh search
//         message: 'No categories found containing this family code.',
//         categories: null,
//       };
//     }

//     const formattedCategories: FoundCategoryDisplay[] = categoryLevel3Matches.map(cat3 => ({
//       level1: {
//         id: cat3.level2.level1.id,
//         name: cat3.level2.level1.name,
//         nameRO: cat3.level2.level1.nameRO,
//         imageUrl: cat3.level2.level1.imageUrl,
//       },
//       level2: {
//         id: cat3.level2.id,
//         name: cat3.level2.name,
//         nameRO: cat3.level2.nameRO,
//         imageUrl: cat3.level2.imageUrl,
//       },
//       level3: {
//         id: cat3.id,
//         name: cat3.name,
//         nameRO: cat3.nameRO,
//         familyCode: cat3.familyCode,
//         imageUrl: cat3.imageUrl,
//       },
//     }));

//     return {
//       ...initialSearchFormState, // Reset to initial state for a fresh search
//       message: `${formattedCategories.length} categories found!`,
//       categories: formattedCategories,
//     };
//   } catch (error) {
//     console.error('Error searching categories:', error);
//     return {
//       ...initialSearchFormState, // Reset to initial state for a fresh search
//       message: 'An unexpected error occurred.',
//       categories: null,
//     };
//   } finally {
//     await prisma.$disconnect();
//   }
// }