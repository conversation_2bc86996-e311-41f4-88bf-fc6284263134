{"name": "database-parts", "version": "0.1.0", "private": true, "scripts": {"import-categs": "tsx scripts/importCategs.ts", "import-class": "tsx scripts/importClass.ts", "import-parts": "tsx scripts/importParts.ts", "import-priceList": "tsx scripts/importPriceList.ts", "import-priceAM": "tsx scripts/importPriceAM.ts", "import-attributes": "tsx scripts/importAttributes.ts", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "loglevel": "^1.9.2", "lucide-react": "^0.509.0", "next": "15.3.1", "next-logger": "^5.0.1", "next-themes": "^0.4.6", "nodemailer": "^7.0.6", "papaparse": "^5.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "recharts": "^3.2.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tsx": "^4.19.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^7.0.1", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.8.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}