"use server";

import prisma from "@/app/utils/db";
import { logError } from "@/lib/logger";
import { ShowroomData } from "@/app/zod/showroomSchemas";

export interface GetShowroomsOptions {
  includeInactive?: boolean;
  sortBy?: "name" | "code" | "city" | "sortOrder";
  sortOrder?: "asc" | "desc";
  search?: string;
}

export interface GetShowroomsResult {
  showrooms: ShowroomData[];
  totalCount: number;
  activeCount: number;
  inactiveCount: number;
}

/**
 * Get all showrooms with optional filtering and sorting
 */
export async function getShowrooms(options: GetShowroomsOptions = {}): Promise<GetShowroomsResult> {
  try {
    const {
      includeInactive = true,
      sortBy = "sortOrder",
      sortOrder = "asc",
      search
    } = options;

    // Build where clause
    const where: any = {};
    
    if (!includeInactive) {
      where.isActive = true;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { city: { contains: search, mode: "insensitive" } },
        { county: { contains: search, mode: "insensitive" } },
      ];
    }

    // Build orderBy clause
    let orderBy: any = {};
    
    if (sortBy === "sortOrder") {
      orderBy = [
        { sortOrder: { sort: sortOrder, nulls: "last" } },
        { name: "asc" } // Secondary sort by name
      ];
    } else {
      orderBy = { [sortBy]: sortOrder };
    }

    // Get showrooms
    const showrooms = await prisma.showroom.findMany({
      where,
      orderBy,
      select: {
        id: true,
        code: true,
        name: true,
        address1: true,
        address2: true,
        city: true,
        county: true,
        postalCode: true,
        phone: true,
        email: true,
        program: true,
        isActive: true,
        sortOrder: true,
      }
    });

    // Get counts
    const [totalCount, activeCount] = await Promise.all([
      prisma.showroom.count(),
      prisma.showroom.count({ where: { isActive: true } })
    ]);

    const inactiveCount = totalCount - activeCount;

    return {
      showrooms,
      totalCount,
      activeCount,
      inactiveCount
    };
  } catch (error) {
    logError("Error fetching showrooms:", error);
    return {
      showrooms: [],
      totalCount: 0,
      activeCount: 0,
      inactiveCount: 0
    };
  }
}

/**
 * Get a single showroom by ID
 */
export async function getShowroomById(id: string): Promise<ShowroomData | null> {
  try {
    const showroom = await prisma.showroom.findUnique({
      where: { id },
      select: {
        id: true,
        code: true,
        name: true,
        address1: true,
        address2: true,
        city: true,
        county: true,
        postalCode: true,
        phone: true,
        email: true,
        program: true,
        isActive: true,
        sortOrder: true,
      }
    });

    return showroom;
  } catch (error) {
    logError(`Error fetching showroom with ID ${id}:`, error);
    return null;
  }
}

/**
 * Get a single showroom by code
 */
export async function getShowroomByCode(code: string): Promise<ShowroomData | null> {
  try {
    const showroom = await prisma.showroom.findUnique({
      where: { code },
      select: {
        id: true,
        code: true,
        name: true,
        address1: true,
        address2: true,
        city: true,
        county: true,
        postalCode: true,
        phone: true,
        email: true,
        program: true,
        isActive: true,
        sortOrder: true,
      }
    });

    return showroom;
  } catch (error) {
    logError(`Error fetching showroom with code ${code}:`, error);
    return null;
  }
}

/**
 * Get active showrooms for dropdowns/selects
 */
export async function getActiveShowrooms(): Promise<Pick<ShowroomData, "id" | "code" | "name">[]> {
  try {
    const showrooms = await prisma.showroom.findMany({
      where: { isActive: true },
      select: {
        id: true,
        code: true,
        name: true,
      },
      orderBy: [
        { sortOrder: { sort: "asc", nulls: "last" } },
        { name: "asc" }
      ]
    });

    return showrooms;
  } catch (error) {
    logError("Error fetching active showrooms:", error);
    return [];
  }
}

/**
 * Check if a showroom code is available
 */
export async function isShowroomCodeAvailable(code: string, excludeId?: string): Promise<boolean> {
  try {
    const where: any = { code };
    
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const existingShowroom = await prisma.showroom.findUnique({
      where,
      select: { id: true }
    });

    return !existingShowroom;
  } catch (error) {
    logError(`Error checking showroom code availability for ${code}:`, error);
    return false;
  }
}
