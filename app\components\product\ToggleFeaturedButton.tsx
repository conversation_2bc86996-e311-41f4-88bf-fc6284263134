"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { toggleFeaturedProduct } from "@/app/actions/featuredActions";
import { toast } from "sonner";
import { Star } from "lucide-react";

interface ToggleFeaturedButtonProps {
  productId: string;
  isFeatured: boolean;
}

export default function ToggleFeaturedButton({ 
  productId, 
  isFeatured 
}: ToggleFeaturedButtonProps) {
  const [isPending, startTransition] = useTransition();
  const [currentFeatured, setCurrentFeatured] = useState(isFeatured);

  const handleToggleFeatured = () => {
    startTransition(async () => {
      try {
        const result = await toggleFeaturedProduct(productId);
        if (result.status === "SUCCESS") {
          setCurrentFeatured(!currentFeatured);
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch {
        toast.error("An error occurred while updating the product");
      }
    });
  };

  return (
    <Button
      variant={currentFeatured ? "default" : "outline"}
      onClick={handleToggleFeatured}
      disabled={isPending}
      className="flex items-center gap-1"
    >
      <Star className="h-4 w-4" />
      {isPending ? "Updating..." : currentFeatured ? "Remove from Featured" : "Add to Featured"}
    </Button>
  );
}