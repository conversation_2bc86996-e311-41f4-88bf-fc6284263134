import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { logError, logInfo } from "@/lib/logger";
import { NETWORK_PATHS } from "@/config/constants";

/**
 * Dynamic API route to serve banner images by filename
 * Similar to the product image system but for banners
 * 
 * Usage:
 * - GET /api/images/my-banner.jpg - Returns the banner image
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const paramsObject = await params;
    const { filename } = paramsObject;

    // Validate filename
    if (!filename || typeof filename !== "string") {
      return NextResponse.json(
        { error: "Valid filename is required" },
        { status: 400 }
      );
    }

    // Security check: prevent path traversal
    const sanitizedFilename = path.basename(filename);
    if (sanitizedFilename !== filename) {
      logError(`API serveBannerImage -> Path traversal attempt: ${filename}`);
      return NextResponse.json(
        { error: "Invalid filename" },
        { status: 400 }
      );
    }

    // Construct the full path to the banner image
    const fullPath = path.join(NETWORK_PATHS.PICTURES_STORAGE, sanitizedFilename);
    
    // Additional security check: ensure the path is within the pictures directory
    const normalizedPicturesPath = path.resolve(NETWORK_PATHS.PICTURES_STORAGE);
    const normalizedFullPath = path.resolve(fullPath);
    
    if (!normalizedFullPath.startsWith(normalizedPicturesPath)) {
      logError(`API serveBannerImage -> Path traversal attempt: ${filename}`);
      return NextResponse.json(
        { error: "Invalid path" },
        { status: 400 }
      );
    }

    // Check if file exists
    try {
      await fs.access(fullPath);
    } catch {
      logError(`API serveBannerImage -> Banner image not found: ${fullPath}`);
      return NextResponse.json(
        { 
          error: "Banner image not found",
          filename: sanitizedFilename
        },
        { status: 404 }
      );
    }

    // Serve the image file
    try {
      const fileBuffer = await fs.readFile(fullPath);
      const fileExtension = path.extname(sanitizedFilename).toLowerCase();
      
      // Determine content type
      let contentType = "application/octet-stream";
      switch (fileExtension) {
        case ".jpg":
        case ".jpeg":
          contentType = "image/jpeg";
          break;
        case ".png":
          contentType = "image/png";
          break;
        case ".webp":
          contentType = "image/webp";
          break;
        case ".gif":
          contentType = "image/gif";
          break;
      }

      // Set cache headers for better performance
      const headers = new Headers({
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=31536000, immutable", // Cache for 1 year
        "Content-Length": fileBuffer.length.toString(),
      });

      logInfo(`Banner Image API -> Served ${sanitizedFilename}`);

      return new NextResponse(fileBuffer as BodyInit, {
        status: 200,
        headers,
      });

    } catch (fileError) {
      logError(`Banner Image API -> Failed to read file ${fullPath}: ${fileError}`);
      return NextResponse.json(
        { error: "Failed to read banner image file" },
        { status: 500 }
      );
    }

  } catch (error) {
    logError(`Banner Image API -> Unexpected error:`, error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}
