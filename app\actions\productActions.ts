"use server"

// import { revalidatePath } from "next/cache";
// import prisma from "../utils/db";
// import { requireAdminOrModerator } from "@/lib/auth-utils";
// import { logError, logInfo } from "@/lib/logger";
// import { ReturnAction } from "./actions";
// import { getImagesForMaterial } from "@/app/lib/imageCache";

/**
 * Get images for a product from the new image index system
 */
// export async function getProductImages(materialNumber: string): Promise<ReturnAction> {
//   try {
//     const actor = await requireAdminOrModerator();
//     const userEmail = actor.email;

//     // Validate material number
//     if (!materialNumber || typeof materialNumber !== 'string') {
//       return {
//         status: "ERROR",
//         message: "Valid material number is required",
//       };
//     }

//     // Get product to verify it exists
//     const product = await prisma.product.findUnique({
//       where: { Material_Number: materialNumber },
//       select: { id: true, Material_Number: true, ImageUrl: true }
//     });

//     if (!product) {
//       logError(`getProductImages -> Product not found: ${materialNumber} by ${userEmail}`);
//       return {
//         status: "ERROR",
//         message: "Product not found",
//       };
//     }

//     // Get cached images
//     const cachedImages = await getImagesForMaterial(materialNumber);

//     logInfo(`getProductImages -> Retrieved images for ${materialNumber} by ${userEmail}`);

//     return {
//       status: "SUCCESS",
//       message: "Product images retrieved successfully",
//       data: {
//         materialNumber,
//         productImages: product.ImageUrl,
//         cachedImages: cachedImages.map(img => ({
//           fileName: img.fileName,
//           sequenceNumber: img.sequenceNumber,
//           fileExtension: img.fileExtension,
//           fileSize: img.fileSize,
//           url: `/api/images/${materialNumber}${img.sequenceNumber > 0 ? `?sequence=${img.sequenceNumber}` : ""}`
//         })),
//         totalProductImages: product.ImageUrl.length,
//         totalCachedImages: cachedImages.length
//       }
//     };

//   } catch (error) {
//     logError(`getProductImages -> Unexpected error:`, error);
//     return {
//       status: "ERROR",
//       message: "An unexpected error occurred while retrieving product images",
//     };
//   }
// }

/**
 * Sync indexed images to a product's ImageUrl field
 */
// export async function syncProductImages(materialNumber: string, replaceExisting: boolean = true): Promise<ReturnAction> {
//   try {
//     const actor = await requireAdminOrModerator();
//     const userEmail = actor.email;

//     // Validate material number
//     if (!materialNumber || typeof materialNumber !== 'string') {
//       return {
//         status: "ERROR",
//         message: "Valid material number is required",
//       };
//     }

//     // Get product
//     const product = await prisma.product.findUnique({
//       where: { Material_Number: materialNumber },
//       select: { id: true, Material_Number: true, ImageUrl: true }
//     });

//     if (!product) {
//       logError(`syncProductImages -> Product not found: ${materialNumber} by ${userEmail}`);
//       return {
//         status: "ERROR",
//         message: "Product not found",
//       };
//     }

//     // Get cached images
//     const cachedImages = await getImagesForMaterial(materialNumber);
//     const cachedImageFileNames = cachedImages.map(img => img.fileName);
//     const defaultPlaceholder = 'https://op47vimj99.ufs.sh/f/6Hnm5nafTbm964jRPnfTbm9EeHnDOzysS6K5X27Upql8xtjN';

//     let newImageUrls: string[];

//     if (replaceExisting) {
//       // Replace all existing images with cached images
//       newImageUrls = cachedImageFileNames.length > 0 ? cachedImageFileNames : [defaultPlaceholder];
//     } else {
//       // Merge existing real images with cached images
//       const existingRealImages = product.ImageUrl.filter(img =>
//         !img.includes('op47vimj99.ufs.sh') && !img.startsWith('http')
//       );

//       const allImages = [...new Set([...existingRealImages, ...cachedImageFileNames])];
//       newImageUrls = allImages.length > 0 ? allImages : [defaultPlaceholder];
//     }

//     // Update the product
//     const updatedProduct = await prisma.product.update({
//       where: { Material_Number: materialNumber },
//       data: {
//         ImageUrl: newImageUrls,
//         updatedBy: userEmail,
//         last_updated_at: new Date(),
//       },
//       select: { id: true, Material_Number: true, ImageUrl: true }
//     });

//     // Revalidate the product page
//     revalidatePath("/product");
//     revalidatePath(`/product/${materialNumber}`);

//     logInfo(`AUDIT: Product images synced for ${materialNumber}, ${cachedImageFileNames.length} cached images, total ${updatedProduct.ImageUrl.length} by ${userEmail}`);

//     return {
//       status: "SUCCESS",
//       message: `Synced ${cachedImageFileNames.length} images for product ${materialNumber}`,
//       data: {
//         materialNumber,
//         previousImageCount: product.ImageUrl.length,
//         newImageCount: updatedProduct.ImageUrl.length,
//         cachedImages: cachedImageFileNames.length,
//         replaceExisting
//       }
//     };

//   } catch (error) {
//     logError(`syncProductImages -> Unexpected error:`, error);
//     return {
//       status: "ERROR",
//       message: "An unexpected error occurred while syncing product images",
//     };
//   }
// }
