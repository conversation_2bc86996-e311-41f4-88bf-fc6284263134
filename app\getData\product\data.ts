"server-only"

import prisma from "@/app/utils/db";
import { ProductSearchSchema } from "@/app/zod/zod";
import { ProductAttributeHistory } from "@/generated/prisma";
import { logError, logInfo } from "@/lib/logger";
import { getCurrentUser } from "../user/data";
import { ProductAttribute, ProductDetailsInterface } from "@/types/Types";

export async function getProductAttributes(productCode: string): Promise<ProductAttribute[]>{
    const user = await getCurrentUser()
    const userEmail = user?.email
    try{
        const parsed = ProductSearchSchema.safeParse({materialNumber: productCode});
        if (!parsed.success) {
                logError(`getProductAttributes -> Error at parsing: ${parsed.error} by ${userEmail}`)
            return []
        }

        const {materialNumber} = parsed.data

        const productExist = await prisma.product.findUnique({
            where: { Material_Number: materialNumber },
        });

        if (!productExist) {
                logError(`getProductAttributes -> Product "${materialNumber}" not found by ${userEmail}`)
            return []
        }

        const productAttributes = await prisma.productAttribute.findMany({
            where: {Material_Number: materialNumber},
            select: {
                key: true,
                value: true
            }
        });

        if(!productAttributes){
                logError(`getProductAttributes -> Product "${materialNumber}" has no attributes by ${userEmail}`)
            return[]
        }

            logInfo(`getProductAttributes -> Product "${materialNumber}" has the attributes: ${productAttributes.map((a) => `${a.key} -- ${a.value}` )} by ${userEmail}`)

        return productAttributes
    }catch(e){
            logError(`getProductAttributes -> Unexpected error for product "${productCode}": ${e}  by ${userEmail}`)
        return[]
    }
}

export async function getProductDetails(productCode: string): Promise<ProductDetailsInterface | null> {
    const user = await getCurrentUser()
    const userEmail = user?.email
  try {
    const parsed = ProductSearchSchema.safeParse({materialNumber: productCode});
    if (!parsed.success) {
      logError(`getProductDetails -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return null
    }

    const {materialNumber} = parsed.data

    const productExist = await prisma.product.findUnique({
      where: { Material_Number: materialNumber },
    });

    if (!productExist) {
      logError(`getProductDetails -> Product not found by ${userEmail}`)
      return null
    }

    // Image sync will be handled by the new system

    return {
        id: productExist.id,
        Material_Number: productExist.Material_Number,
        ImageUrl: productExist.ImageUrl,
        Net_Weight: productExist.Net_Weight,
        Description_Local: productExist.Description_Local,
        Base_Unit_Of_Measur: productExist.Base_Unit_Of_Measur,
        Cross_Plant: productExist.Cross_Plant,
        New_Material: productExist.New_Material,
        HasDiscount: productExist.HasDiscount,
        activeDiscountType: productExist.activeDiscountType,
        Material_Group: productExist.Material_Group,
        PretAM: productExist.PretAM?.toString() || null,
        FinalPrice: productExist.FinalPrice?.toString() || null,
        discountPercentage: productExist.discountPercentage?.toString() || null,
        activeDiscountValue: productExist.activeDiscountValue?.toString() || null,
        createdAt: productExist.createdAt.toISOString(),
        last_updated_at: productExist.last_updated_at.toISOString(),
        IsOnLandingPage: productExist.IsOnLandingPage,
    };

  } catch(e) {
    logError(`getProductDetails -> Unexpected error for product "${productCode}": ${e} by ${userEmail}`)
    return null
  }
}   

export async function getProductAttributesHistory(productCode: string): Promise<ProductAttributeHistory[]>{
    const user = await getCurrentUser()
    const userEmail = user?.email
    try{
        const parsed = ProductSearchSchema.safeParse({materialNumber: productCode});
        if (!parsed.success) {
                logError(`getProductAttributesHistory -> Error at parsing: ${parsed.error} by ${userEmail}`)
            return []
        }

        const {materialNumber} = parsed.data

        const productExist = await prisma.product.findUnique({
            where: { Material_Number: materialNumber },
        });

        if (!productExist) {
                logError(`getProductAttributesHistory -> Product "${materialNumber}" not found by ${userEmail}`)
            return []
        }

        const productAttributesHistory: ProductAttributeHistory[] = await prisma.productAttributeHistory.findMany({
            where: {Material_Number: materialNumber}
        });

        if(!productAttributesHistory){
                logError(`getProductAttributesHistory -> Product "${materialNumber}" has no attributes by ${userEmail}`)
            return[]
        }

            logInfo(`getProductAttributesHistory -> Product "${materialNumber}" with success by ${userEmail}`)

        return productAttributesHistory
    }catch(e){
            logError(`getProductAttributesHistory -> Unexpected error for product "${productCode}": ${e}  by ${userEmail}`)
        return[]
    }
}

export async function getFeaturedProducts(){
    const user = await getCurrentUser()
    const userEmail = user?.email
    try{
        const featuredProducts = await prisma.product.findMany({
            where: {
                IsOnLandingPage: true,
            //isActive: true
            },
            select: {
                id: true,
                Material_Number: true,
                Description_Local: true,
                ImageUrl: true,
                FinalPrice: true,
            },
        })
    
        if(!featuredProducts){
            logError(`getFeaturedProducts -> No featured products found by ${userEmail}`)
            return []
        }
    
        logInfo(`getFeaturedProducts -> Featured products found by ${userEmail}`)

        const parsedFeaturedProducts = featuredProducts.map((p) => ({
                id: p.id,
                Material_Number: p.Material_Number,
                Description_Local: p.Description_Local,
                ImageUrl: p.ImageUrl,
                FinalPrice: p.FinalPrice?.toNumber() || null,
            }))
        
    
        return parsedFeaturedProducts
    }catch(e){
        logError(`getFeaturedProducts -> Unexpected error: ${e}  by ${userEmail}`)
        return []
    }
}