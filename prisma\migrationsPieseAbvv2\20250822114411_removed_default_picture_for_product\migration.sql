/*
  Warnings:

  - You are about to drop the column `showroom` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `showroomCode` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `showroomCode` on the `ServiceRequest` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Order" DROP COLUMN "showroom",
ADD COLUMN     "showroomId" TEXT;

-- AlterTable
ALTER TABLE "Product" ALTER COLUMN "ImageUrl" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Return" DROP COLUMN "showroomCode",
ADD COLUMN     "showroomId" TEXT;

-- AlterTable
ALTER TABLE "ServiceRequest" DROP COLUMN "showroomCode",
ADD COLUMN     "showroomId" TEXT;

-- DropEnum
DROP TYPE "Showroom";

-- CreateTable
CREATE TABLE "Showroom" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "address1" TEXT NOT NULL,
    "address2" TEXT,
    "city" TEXT NOT NULL,
    "county" TEXT NOT NULL,
    "postalCode" TEXT,
    "phone" TEXT NOT NULL,
    "email" TEXT,
    "program" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER,

    CONSTRAINT "Showroom_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Showroom_code_key" ON "Showroom"("code");

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_showroomId_fkey" FOREIGN KEY ("showroomId") REFERENCES "Showroom"("code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_showroomId_fkey" FOREIGN KEY ("showroomId") REFERENCES "Showroom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_showroomId_fkey" FOREIGN KEY ("showroomId") REFERENCES "Showroom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
