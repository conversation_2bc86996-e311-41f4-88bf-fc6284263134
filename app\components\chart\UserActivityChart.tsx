"use client";

import {
  Re<PERSON>onsive<PERSON><PERSON>r,
  AreaChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Area,
} from "recharts";

interface UserActivityData {
  date: string;
  activeUsers: number;
}

interface UserActivityChartProps {
  data: UserActivityData[];
}

const aggregateData = (data: UserActivityData[]) => {
  const aggregated = data.reduce((acc: Record<string, number>, curr) => {
    if (acc[curr.date]) {
      acc[curr.date] += curr.activeUsers;
    } else {
      acc[curr.date] = curr.activeUsers;
    }
    return acc;
  }, {});

  return Object.keys(aggregated).map((date) => ({
    date,
    activeUsers: aggregated[date],
  }));
};

export function UserActivityChart({ data }: UserActivityChartProps) {
  const processedData = aggregateData(data);
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={processedData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Area
          type="monotone"
          dataKey="activeUsers"
          stroke="#8b5cf6"
          fill="#8b5cf6"
          fillOpacity={0.3}
          name="Active Users"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}
