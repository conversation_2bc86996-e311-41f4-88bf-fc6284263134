"use client";

import { useTransition } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { changeUserStatus, deleteUser } from "@/app/actions/userActions";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  MoreHorizontal, 
  Edit, 
  Trash, 
  Ban, 
  Eye,
  CheckCircle,
  XCircle
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale"; // Import a specific locale
import { Pagination } from "@/components/ui/pagination";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImage: string;
  role: string;
  isActive: boolean;
  isSuspended: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  deletedAt: Date | null;
  accessGroups: { id: string; name: string }[];
}

interface UserListProps {
  users: User[];
  totalUsers: number;
  totalPages: number;
  currentPage: number;
  perPage: number;
}

export default function UserList({ 
  users, 
  totalUsers, 
  totalPages, 
  currentPage, 
  perPage 
}: UserListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Get current sort parameters
  const currentSort = searchParams.get("sort") || "createdAt";
  const currentOrder = searchParams.get("order") || "desc";
  
  // Function to handle sorting
  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams);
    
    // If already sorting by this column, toggle order
    if (currentSort === column) {
      params.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      // Otherwise, sort by this column in ascending order
      params.set("sort", column);
      params.set("order", "asc");
    }
    
    router.push(`/users?${params.toString()}`);
  };
  
  // Function to handle pagination
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/users?${params.toString()}`);
  };
  
  // Function to get sort indicator
  const getSortIndicator = (column: string) => {
    if (currentSort !== column) return null;
    return currentOrder === "asc" ? "↑" : "↓";
  };
  
  // Function to get user status badge
  const getUserStatusBadge = (user: User) => {
    if (user.deletedAt) {
      return <Badge variant="destructive">Deleted</Badge>;
    }
    if (user.isSuspended) {
      return <Badge variant="secondary">Suspended</Badge>;
    }
    if (!user.isActive) {
      return <Badge variant="outline">Inactive</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };
  
  // Function to get user role badge
  const getUserRoleBadge = (role: string) => {
    switch (role) {
      case "administAB":
        return <Badge variant="default">Administrator</Badge>;
      case "moderatorAB":
        return <Badge variant="outline">Moderator</Badge>;
      case "angajatAB":
        return <Badge variant="secondary">Employee</Badge>;
      case "fourLvlAdminAB":
        return <Badge variant="default">L4 Admin</Badge>;
      case "fourLvlInregistratAB":
        return <Badge variant="outline">L4 User</Badge>;
      default:
        return <Badge variant="outline">User</Badge>;
    }
  };

  // Function to handle user deletion
  const handleDeleteUser = (userId: string) => {
    startTransition(async () => {
      try {
        const result = await deleteUser(userId);
        if (result.success) {
          toast.success("User deleted successfully");
          router.refresh();
        } else {
          toast.error("Failed to delete user");
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error("Error deleting user:", error);
      }
    });
  };

  // Function to handle user activation/deactivation
  const handleToggleActive = (userId: string, isActive: boolean) => {
    startTransition(async () => {
      try {
        const result = await changeUserStatus({
          userId,
          isActive: !isActive,
          isSuspended: false,
          suspensionReason: ""
        });
        
        if (result.status === "SUCCESS") {
          toast.success(result.message);
          router.refresh();
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error("Error changing user status:", error);
      }
    });
  };

  // Function to handle user suspension/unsuspension
  const handleToggleSuspension = (userId: string, isSuspended: boolean) => {
    startTransition(async () => {
      try {
        const suspensionReason = isSuspended ? "" : "Administrative action";
        const result = await changeUserStatus({
          userId,
          isActive: true,
          isSuspended: !isSuspended,
          suspensionReason
        });
        
        if (result.status === "SUCCESS") {
          toast.success(result.message);
          router.refresh();
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error("Error changing suspension status:", error);
      }
    });
  };
  
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort("role")}
              >
                Role {getSortIndicator("role")}
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort("lastLoginAt")}
              >
                Last Login {getSortIndicator("lastLoginAt")}
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort("createdAt")}
              >
                Created {getSortIndicator("createdAt")}
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={user.profileImage} alt={`${user.firstName} ${user.lastName}`} />
                        <AvatarFallback>
                          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.firstName} {user.lastName}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getUserRoleBadge(user.role)}</TableCell>
                  <TableCell>{getUserStatusBadge(user)}</TableCell>
                  <TableCell>
                    {user.lastLoginAt ? (
                      <span title={format(new Date(user.lastLoginAt), 'MM/dd/yyyy, h:mm:ss a', { locale: enUS })}>
                        {formatDistanceToNow(new Date(user.lastLoginAt), { 
                          addSuffix: true,
                          locale: enUS // Use the same locale for both server and client
                        })}
                      </span>
                    ) : (
                      <span className="text-gray-400">Never</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span title={format(new Date(user.createdAt), 'MM/dd/yyyy, h:mm:ss a', { locale: enUS })}>
                      {formatDistanceToNow(new Date(user.createdAt), { 
                        addSuffix: true,
                        locale: enUS // Use the same locale for both server and client
                      })}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <Link href={`/users/${user.id}`}>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Profile
                          </DropdownMenuItem>
                        </Link>
                        <Link href={`/users/${user.id}/edit`}>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                        </Link>
                        {/* <DropdownMenuSeparator />
                        <Link href={`/users/${user.id}/permissions`}>
                          <DropdownMenuItem>
                            <Shield className="mr-2 h-4 w-4" />
                            Permissions
                          </DropdownMenuItem>
                        </Link>
                        <Link href={`/users/${user.id}/groups`}>
                          <DropdownMenuItem>
                            <UserCog className="mr-2 h-4 w-4" />
                            Access Groups
                          </DropdownMenuItem>
                        </Link> */}
                        <DropdownMenuSeparator />
                        {user.isActive ? (
                          <DropdownMenuItem onClick={() => handleToggleActive(user.id, user.isActive)}>
                            <XCircle className="mr-2 h-4 w-4" />
                            {isPending ? "Deactivating..." : "Deactivate"}
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => handleToggleActive(user.id, user.isActive)}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            {isPending ? "Activating..." : "Activate"}
                          </DropdownMenuItem>
                        )}
                        {user.isSuspended ? (
                          <DropdownMenuItem onClick={() => handleToggleSuspension(user.id, user.isSuspended)}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            {isPending ? "Unsuspending..." : "Unsuspend"}
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => handleToggleSuspension(user.id, user.isSuspended)}>
                            <Ban className="mr-2 h-4 w-4" />
                            {isPending ? "Suspending..." : "Suspend"}
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          className="text-destructive"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          {isPending ? "Deleting..." : "Delete"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-4 border-t">
          <div className="text-sm text-gray-500">
            Showing <span className="font-medium">{(currentPage - 1) * perPage + 1}</span> to{" "}
            <span className="font-medium">
              {Math.min(currentPage * perPage, totalUsers)}
            </span>{" "}
            of <span className="font-medium">{totalUsers}</span> users
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}





