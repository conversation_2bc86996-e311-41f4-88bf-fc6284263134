"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { formatCurrency, formatDate } from "@/app/utils/formatters";
import { ReturnOrders } from "@/types/order";
import { Pagination } from "@/components/ui/pagination";

interface OrderListProps {
  orders: ReturnOrders[];
  totalOrders: number;
  totalPages: number;
  currentPage: number;
  perPage: number;
}

export default function OrderList({
  orders,
  totalOrders,
  totalPages,
  currentPage,
  perPage
}: OrderListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current search and filter parameters
  const currentQuery = searchParams.get("query") || "";
  const currentStatus = searchParams.get("status") || "all";
  const currentSort = searchParams.get("sort") || "createdAt";
  const currentOrder = searchParams.get("order") || "desc";

  const [searchTerm, setSearchTerm] = useState(currentQuery);
  const [statusFilter, setStatusFilter] = useState(currentStatus);

  // Function to handle search
  const handleSearch = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("query", value);
    } else {
      params.delete("query");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/orders?${params.toString()}`);
  };

  // Function to handle status filter
  const handleStatusFilter = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value && value !== "all") {
      params.set("status", value);
    } else {
      params.delete("status");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/orders?${params.toString()}`);
  };

  // Function to handle sorting
  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams);

    // If already sorting by this column, toggle order
    if (currentSort === column) {
      params.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      // Otherwise, sort by this column in ascending order
      params.set("sort", column);
      params.set("order", "asc");
    }

    router.push(`/orders?${params.toString()}`);
  };

  // Function to handle pagination
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/orders?${params.toString()}`);
  };

  // Function to get sort indicator
  const getSortIndicator = (column: string) => {
    if (currentSort !== column) return null;
    return currentOrder === "asc" ? "↑" : "↓";
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      plasata: { label: "Deschisa", variant: "default" },
      procesata: { label: "Processing", variant: "secondary" },
      completa: { label: "Inchisa", variant: "primary" },
      livrata: { label: "Delivered", variant: "success" },
      anulata: { label: "Anulata", variant: "destructive" },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: "outline" };
    
    return (
      <Badge variant={statusInfo.variant as "default" | "destructive" | "outline" | "secondary"}>{statusInfo.label}</Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Orders</CardTitle>
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="flex-1">
            <Input
              placeholder="Search by order number or email"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                // Debounce search
                setTimeout(() => handleSearch(e.target.value), 300);
              }}
            />
          </div>
          <div className="w-full sm:w-48">
            <Select value={statusFilter} onValueChange={(value) => {
              setStatusFilter(value);
              handleStatusFilter(value);
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toate</SelectItem>
                <SelectItem value="plasata">Deschise</SelectItem>
                <SelectItem value="completa">Inchise</SelectItem>
                <SelectItem value="anulata">Anulate</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">No orders found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("orderNumber")}
                  >
                    Order Number {getSortIndicator("orderNumber")}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("createdAt")}
                  >
                    Date {getSortIndicator("createdAt")}
                  </TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("totalAmount")}
                  >
                    Amount {getSortIndicator("totalAmount")}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("orderStatus")}
                  >
                    Status {getSortIndicator("orderStatus")}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("paymentStatus")}
                  >
                    Payment {getSortIndicator("paymentStatus")}
                  </TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.orderNumber}</TableCell>
                    <TableCell>{formatDate(order.createdAt)}</TableCell>
                    <TableCell>
                      {order.user ? (
                        <div>
                          <div>{`${order.user.firstName || ''} ${order.user.lastName || ''}`}</div>
                          <div className="text-sm text-muted-foreground">{order.user.email}</div>
                        </div>
                      ) : (
                        "Unknown"
                      )}
                    </TableCell>
                    <TableCell>{formatCurrency(order.totalAmount)}</TableCell>
                    <TableCell>{getStatusBadge(order.orderStatus)}</TableCell>
                    <TableCell>
                      <Badge variant={order.isPaid ? "default" : "secondary"}>
                        {order.isPaid ? "Paid" : "Pending"}
                      </Badge>
                    </TableCell>
                    <TableCell>{order.notes}</TableCell>
                    <TableCell className="text-right">
                      <Link href={`/orders/${order.id}`}>
                        <Button variant="outline" size="sm">View</Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-4 border-t">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">{(currentPage - 1) * perPage + 1}</span> to{" "}
              <span className="font-medium">
                {Math.min(currentPage * perPage, totalOrders)}
              </span>{" "}
              of <span className="font-medium">{totalOrders}</span> orders
            </div>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}






