"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  Home, 
  Package, 
  ShoppingCart, 
  Users, 
  BarChart,
  Tag,
  Layers,
  Star,
  PictureInPicture,
  MapPinHouse
} from "lucide-react";

import { cn } from "@/lib/utils";

interface NavItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  adminOnly?: boolean;
}

export function DashboardNavigation() {
  const pathname = usePathname();
  
  const navItems: NavItem[] = [
    {
      label: "Home",
      href: "/",
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: "Products",
      href: "/product",
      icon: <Package className="h-4 w-4" />,
    },
    {
      label: "Categories",
      href: "/categories",
      icon: <Layers className="h-4 w-4" />,
    },
    {
      label: "Orders",
      href: "/orders",
      icon: <ShoppingCart className="h-4 w-4" />,
    },
    {
      label: "Returns",
      href: "/returns",
      icon: <PictureInPicture className="h-4 w-4" />,
    },
    {
      label: "Service",
      href: "/services",
      icon: <BarChart className="h-4 w-4" />,
    },
    {
      label: "Groups",
      href: "/groups",
      icon: <Users className="h-4 w-4" />,
      adminOnly: true,
    },
    {
      label: "Discounts",
      href: "/discounts",
      icon: <Tag className="h-4 w-4" />,
    },
    {
      label: "Users",
      href: "/users",
      icon: <Users className="h-4 w-4" />,
      adminOnly: true,
    },
    {
      label: "Featured",
      href: "/featured",
      icon: <Star className="h-4 w-4" />,
      adminOnly: true,
    },
    {
      label: "Banners",
      href: "/banner",
      icon: <PictureInPicture className="h-4 w-4" />,
    },
    {
      label: "Showrooms",
      href: "/showrooms",
      icon: <MapPinHouse className="h-4 w-4" />,
    },
  ];

  // Filter admin-only items if needed
  // In a real app, you'd check the user's role
  const filteredNavItems = navItems;

  const userId = true
  if(!userId){
    return null;
  }

  return (
    <>
      {filteredNavItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "flex items-center gap-2 text-sm font-medium transition-colors hover:text-primary",
            pathname === item.href
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          {item.icon}
          {item.label}
        </Link>
      ))}
    </>
  );
}
