"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { DiscountUpdateFormValues, updateDiscountSchema } from "@/app/zod/zod";
import { DiscountUpdateAction } from "@/app/actions/actions";
import { ServerError } from "@/types/Types";

interface UpdateFormProps {
  id: string;
  defaultValues: Omit<DiscountUpdateFormValues, "id">;
}

export default function DiscountUpdateForm({ id, defaultValues }: UpdateFormProps) {
  const [serverError, setServerError] = useState<ServerError | null>(null);
  const router = useRouter();

  const {
    register,
    reset,
    setError,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<DiscountUpdateFormValues>({
    resolver: zodResolver(updateDiscountSchema),
    defaultValues: { ...defaultValues, id },
  });

  const onSubmit = async (data: DiscountUpdateFormValues) => {
      setServerError(null);
      try {
        console.log("presed")
        const response = await DiscountUpdateAction({
          ...data
        });
          if(response.status === "SUCCESS"){
            router.push("/discounts");
            reset();
            toast.success(response.message);
          }else{
            toast.error(response.message)

            if (response.fieldErrors) {
              Object.entries(response.fieldErrors).forEach(([field, message]) => {
                setError(field as keyof DiscountUpdateFormValues, {
                  type: "server",
                  message,
                });
              });
            } else {
              setServerError({ message: String(response.message) });
            }
          }        
        } catch (error) {
          console.error("Unexpected error:", error);
          setServerError({ message: "Unexpected error occurred. Please try again later." });
        }
  };

  return (
    <>
    {serverError && (
      <p className="text-red-500 mb-2">{serverError.message}</p>
    )}

    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <input type="hidden" {...register("id")} value={id} />

      <div>
        <label className="block mb-1">Name:</label>
        <input
          type="text"
          {...register("name")}
          className="w-full p-2 border rounded"
        />
        {errors.name && <p className="text-red-500">{errors.name.message}</p>}
      </div>

      <div>
        <label className="block mb-1">Description:</label>
        <input
          type="text"
          {...register("description")}
          className="w-full p-2 border rounded"
        />
        {errors.description && <p className="text-red-500">{errors.description.message}</p>}
      </div>

      <div>
        <label className="block mb-1">Type:</label>
        <select {...register("type")} className="w-full p-2 border rounded">
          <option value="PERCENTAGE">Percentage</option>
          <option value="FIXED_AMOUNT">Fixed Amount</option>
          <option value="NEW_PRICE">New Price</option>
        </select>
        {errors.type && <p className="text-red-500">{errors.type.message}</p>}
      </div>

      <div>
        <label className="block mb-1">Value:</label>
        <input
          type="number"
          step="0.01"
          {...register("value", { valueAsNumber: true })}
          className="w-full p-2 border rounded"
        />
        {errors.value && <p className="text-red-500">{errors.value.message}</p>}
      </div>

      <div>
        <label className="block mb-1">Active:</label>
        <input
          type="checkbox"
          {...register("active")}
          defaultChecked={defaultValues.active}
        />
        {errors.active && <p className="text-red-500">{errors.active.message}</p>}
      </div>

      <div>
        <label className="block mb-1">Start Date: <span className="text-sm text-blue-400">{defaultValues.startDate.toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false, // Use 24-hour format
          // timeZone: 'Europe/Bucharest' // Optional: Specify a particular timezone for display
        })}</span></label>
        <input
          type="datetime-local"
          {...register("startDate")}
          className="w-full p-2 border rounded"
        />
        {errors.startDate && <p className="text-red-500">{errors.startDate.message}</p>}
      </div>

      <div>
        <label className="block mb-1">End Date: <span className="text-sm text-blue-400">{defaultValues.endDate.toLocaleString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false, // Use 24-hour format
    // timeZone: 'Europe/Bucharest' // Optional: Specify a particular timezone for display
  })}</span></label>
        <input
          type="datetime-local"
          {...register("endDate")}
          className="w-full p-2 border rounded"
        />
        {errors.endDate && <p className="text-red-500">{errors.endDate.message}</p>}
      </div>

      <Button
        type="submit"
        disabled={isSubmitting}
        variant="default"
        className="w-full"
      >
        {isSubmitting ? "Updating..." : "Update Discount"}
      </Button>
    </form>
    </>
  );
}
