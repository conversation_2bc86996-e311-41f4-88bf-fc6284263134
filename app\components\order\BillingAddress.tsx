interface BillingAddressProps {
   billingAddress: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    address: string;
    userId: string;
    fullName: string;
    companyName: string | null;
    city: string;
    county: string;
    cui: string | null;
    bank: string | null;
    iban: string | null;
    isDefault: boolean;
} | null
}

export default function BillingAddress({ billingAddress } : BillingAddressProps) {
  if (!billingAddress) {
    return <p className="text-muted-foreground">No address information available</p>;
  }

  return (
    <div className="space-y-1">

      <p className="font-medium">{billingAddress.fullName}</p>
      <p>{billingAddress.address}</p>
      {billingAddress.companyName && <p>{billingAddress.companyName}</p>}
      <p>
        {billingAddress.city}, {billingAddress.county} {billingAddress.cui}
      </p>
      <p>{billingAddress.iban}</p>
      {billingAddress.bank && <p className="mt-2">Bank: {billingAddress.bank}</p>}
    </div>
  );
}