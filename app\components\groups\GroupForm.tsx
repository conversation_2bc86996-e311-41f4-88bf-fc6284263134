"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

import { UserGroup } from "@/generated/prisma";

const groupSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

type GroupFormValues = z.infer<typeof groupSchema>;

interface GroupFormProps {
  group?: UserGroup;
  onSubmit: (data: GroupFormValues) => Promise<void>;
}

export default function GroupForm({ group, onSubmit }: GroupFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<GroupFormValues>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      name: group?.name || "",
      description: group?.description || "",
    },
  });

  const handleSubmit = async (values: GroupFormValues) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
      toast.success(group ? "Group updated successfully" : "Group created successfully");
      router.push("/groups");
    } catch {
      toast.error("Failed to save group");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Group Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter group name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Enter group description" 
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Optional description of what this group is for
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : group ? "Update Group" : "Create Group"}
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.push("/groups")}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}