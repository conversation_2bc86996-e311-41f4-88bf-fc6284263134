import { Metada<PERSON> } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getShowroomById } from "@/app/getData/showroom/data";
import ShowroomForm from "@/app/components/showroom/ShowroomForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "Edit Showroom",
  description: "Edit an existing showroom",
};

interface EditShowroomPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditShowroomPage({ params }: EditShowroomPageProps) {
  await requireAdminOrModerator();
  
  const paramsObject = await params;
  const showroomId = paramsObject.id;
  
  const showroom = await getShowroomById(showroomId);
  
  if (!showroom) {
    notFound();
  }
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/showrooms">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Showrooms
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Edit Showroom</h1>
          <p className="text-muted-foreground mt-1">
            Update information for {showroom.name} ({showroom.code})
          </p>
        </div>
      </div>
      
      <ShowroomForm showroom={showroom} isEditing={true} />
    </div>
  );
}
