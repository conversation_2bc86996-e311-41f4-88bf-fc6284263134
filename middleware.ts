import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define public routes that don't require authentication
const publicRoutes = [
  //'/',                // Homepage
  '/sign-in(.*)',     // Sign in pages
  '/sign-up(.*)',     // Sign up pages
  // '/api/webhook(.*)', // Webhooks
  // '/_next(.*)',       // Next.js assets
  // '/favicon.ico',     // Favicon
   '/api/(.*)(.js|.css|.png|.jpg|.svg)' // Static assets
];

// Create a matcher for public routes
const isPublicRoute = createRouteMatcher(publicRoutes);

export default clerkMiddleware(async (auth, req) => {
  // If it's not a public route, protect it
  if (!isPublicRoute(req)) {
    await auth.protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};


